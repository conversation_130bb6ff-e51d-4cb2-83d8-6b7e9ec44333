#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脚本：运行10个用户的意图分析
"""

import json
import logging
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from logger_config import setup_logger
from macit_framework import MACITFramework
from data_utils import get_all_user_ids

# 设置日志
logger = setup_logger('test_10_users')


def format_results_for_display(results):
    """
    将结果格式化为上下文-回答-意图的形式

    Args:
        results: 分析结果列表

    Returns:
        格式化后的结果字符串
    """
    formatted_output = []

    for i, result in enumerate(results, 1):
        user_id = result.get('user_id', 'Unknown')
        interaction_index = result.get('interaction_index', 0)

        # 获取交互信息
        interaction_info = result.get('interaction_info', {})
        context_text = interaction_info.get('context_text', '无上下文')
        action_text = interaction_info.get('action_text', '无用户行为')
        action_type = interaction_info.get('action_type', '未知类型')

        # 获取最终意图
        final_intent = result.get('final_intent', {})
        motivation = final_intent.get('motivation', '未分析')
        intention = final_intent.get('intention', '未分析')
        behavior = final_intent.get('behavior', '未分析')

        # 格式化输出
        formatted_output.append(f"""
=== 用户 {i}: {user_id} ===
【上下文】: {context_text[:200]}{'...' if len(context_text) > 200 else ''}

【用户行为】({action_type}): {action_text[:200]}{'...' if len(action_text) > 200 else ''}

【意图分析】:
- 动机: {motivation}
- 意图: {intention}
- 行为: {behavior}

【辩论轮数】: {result.get('debate_rounds', 0)}
【是否达成共识】: {'是' if result.get('consensus_reached', False) else '否'}
""")

    return '\n'.join(formatted_output)


def main():
    """主函数"""
    logger.info("开始测试10个用户的意图分析...")

    # 创建MACIT框架
    try:
        framework = MACITFramework()
        logger.info("MACIT框架初始化成功")
    except Exception as e:
        logger.error(f"MACIT框架初始化失败: {e}")
        return

    # 获取前10个用户ID
    all_user_ids = get_all_user_ids()
    test_user_ids = all_user_ids[:10]

    logger.info(f"将测试以下10个用户: {test_user_ids}")

    results = []

    # 分析每个用户的意图
    for i, user_id in enumerate(test_user_ids, 1):
        logger.info(f"正在分析用户 {user_id} ({i}/10)...")

        try:
            # 分析用户意图（使用第一个交互）
            result = framework.analyze_user_intent(
                user_id=user_id,
                interaction_index=0,
                max_rounds=2,  # 限制为2轮辩论以节省时间
            )

            # 保存分析结果
            output_path = framework.save_analysis_result(
                result=result,
                user_id=user_id,
                interaction_index=0,
            )

            results.append(result)
            logger.info(f"用户 {user_id} 分析完成，结果保存至: {output_path}")

        except Exception as e:
            logger.error(f"分析用户 {user_id} 时出错: {e}")
            # 创建一个错误结果
            error_result = {
                'user_id': user_id,
                'interaction_index': 0,
                'error': str(e),
                'final_intent': {
                    'motivation': '分析失败',
                    'intention': '分析失败',
                    'behavior': '分析失败'
                },
                'debate_rounds': 0,
                'consensus_reached': False,
                'interaction_info': {
                    'context_text': '获取失败',
                    'action_text': '获取失败',
                    'action_type': '未知'
                }
            }
            results.append(error_result)

    # 格式化并显示结果
    logger.info("所有用户分析完成，正在整理结果...")

    formatted_results = format_results_for_display(results)

    # 保存格式化结果到文件
    output_file = Path("output/test_10_users_results.txt")
    output_file.parent.mkdir(exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("MACIT框架 - 10个用户意图分析结果\n")
        f.write("=" * 50 + "\n")
        f.write(formatted_results)

    logger.info(f"格式化结果已保存至: {output_file}")

    # 在控制台显示结果
    print("\n" + "=" * 80)
    print("MACIT框架 - 10个用户意图分析结果")
    print("=" * 80)
    print(formatted_results)

    # 统计信息
    successful_analyses = len([r for r in results if 'error' not in r])
    consensus_reached = len([r for r in results if r.get('consensus_reached', False)])

    print(f"\n【统计信息】:")
    print(f"- 成功分析: {successful_analyses}/10")
    print(f"- 达成共识: {consensus_reached}/10")
    print(f"- 平均辩论轮数: {sum(r.get('debate_rounds', 0) for r in results) / len(results):.1f}")


if __name__ == "__main__":
    main()
