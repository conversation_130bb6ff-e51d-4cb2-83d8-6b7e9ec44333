#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成最终报告：整理所有用户的意图分析结果
"""

import json
import os
from pathlib import Path

def load_user_interaction_data(user_id):
    """加载用户的交互数据"""
    data_file = Path(f"data/cut/intera_data/{user_id}.json")
    if data_file.exists():
        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def generate_final_report():
    """生成最终报告"""
    output_dir = Path("output")
    results = []

    # 遍历所有用户结果
    for user_dir in output_dir.iterdir():
        if user_dir.is_dir():
            result_file = user_dir / "interaction_0.json"
            if result_file.exists():
                with open(result_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)

                # 加载用户交互数据
                user_id = result['user_id']
                interaction_data = load_user_interaction_data(user_id)

                if interaction_data and isinstance(interaction_data, dict) and 'interactions' in interaction_data:
                    interactions = interaction_data['interactions']
                    if len(interactions) > 0:
                        interaction = interactions[0]  # 使用第一个交互

                        # 整理结果
                        formatted_result = {
                            'user_id': user_id,
                            'context': interaction.get('context', {}).get('text', '无上下文'),
                            'user_action': interaction.get('text', '无用户行为'),
                            'action_type': interaction.get('type', '未知类型'),
                            'final_intent': result.get('final_intent', {}),
                            'consensus_reached': result.get('consensus_reached', False),
                            'debate_rounds': result.get('debate_rounds', 0)
                        }
                        results.append(formatted_result)

    # 生成报告
    report_lines = []
    report_lines.append("MACIT框架 - 用户意图分析结果报告")
    report_lines.append("=" * 80)
    report_lines.append(f"分析时间: {Path().cwd()}")
    report_lines.append(f"成功分析用户数: {len(results)}")
    report_lines.append(f"达成共识用户数: {sum(1 for r in results if r['consensus_reached'])}")
    if len(results) > 0:
        report_lines.append(f"平均辩论轮数: {sum(r['debate_rounds'] for r in results) / len(results):.1f}")
    else:
        report_lines.append("平均辩论轮数: 0")
    report_lines.append("")

    for i, result in enumerate(results, 1):
        report_lines.append(f"=== 用户 {i}: {result['user_id']} ===")
        report_lines.append("")

        # 上下文
        context = result['context'][:200] + "..." if len(result['context']) > 200 else result['context']
        report_lines.append(f"【上下文】: {context}")
        report_lines.append("")

        # 用户行为
        action = result['user_action'][:200] + "..." if len(result['user_action']) > 200 else result['user_action']
        report_lines.append(f"【用户行为】({result['action_type']}): {action}")
        report_lines.append("")

        # 意图分析
        final_intent = result['final_intent']
        if final_intent:
            report_lines.append("【意图分析】:")
            report_lines.append(f"- 动机: {final_intent.get('motivation', '未分析')}")
            report_lines.append(f"- 意图: {final_intent.get('intention', '未分析')}")
            report_lines.append(f"- 行为: {final_intent.get('behavior', '未分析')}")
        else:
            report_lines.append("【意图分析】: 分析失败")
        report_lines.append("")

        # 分析信息
        report_lines.append(f"【辩论轮数】: {result['debate_rounds']}")
        report_lines.append(f"【是否达成共识】: {'是' if result['consensus_reached'] else '否'}")
        report_lines.append("")
        report_lines.append("-" * 80)
        report_lines.append("")

    # 保存报告
    report_file = Path("output/final_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

    print(f"最终报告已生成: {report_file}")

    # 在控制台显示报告
    print("\n" + "=" * 80)
    for line in report_lines:
        print(line)

if __name__ == "__main__":
    generate_final_report()
