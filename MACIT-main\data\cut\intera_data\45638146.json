{"user_id": "45638146", "interactions_count": 20, "interactions": [{"conversation_id": "192****************", "tweet_id": "1922270164961394876", "timestamp": "2025-05-13T12:38:16+00:00", "timestamp_unix": 1747139896, "type": "comment", "text": "The 🇨🇳  HQ9 air defence system along with the JF17s & J10s sucked 🍆 in combat. 🇮🇳 slapped 👋 🇵🇰 airforce around like a little b****, hitting 11 air bases and 9 terrorist camps in two separated missions that lasted < 1hr each. Also deep inside Pakistani territories. Those shitty jets and air defence systems you talking about are more of an embarrassment than anything else. You are delusional.", "context": {"type": "tweet", "id": "1922453948704629106", "text": "Ah! The egghead oracle has spoken &amp; lol thinks it is on all goods but no, it’s only on steel. Being the 2nd largest producer, steel tariffs are being negotiated across the board. US  has imposed tariffs of 25% on all countries, incl CN. IN too has imposed 12% tariffs on CN steel.", "author_id": "1697086406303367168", "author_username": "<PERSON>_<PERSON>rdaus"}, "metrics": {"retweet_count": 1, "reply_count": 28, "like_count": 8, "quote_count": 0, "view_count": 4014}}, {"conversation_id": "192****************", "tweet_id": "1922274253820338297", "timestamp": "2025-05-13T12:54:30+00:00", "timestamp_unix": 1747140870, "type": "comment", "text": "@BeijingDai Case in point 👇And we India didn’t need any help in war for America, it’s Pakistan that went crying to its step-dad when it couldn’t take anymore losses. India just showed mercy. ✌️", "context": {"type": "tweet", "id": "1922270164961394876", "text": "The 🇨🇳  HQ9 air defence system along with the JF17s & J10s sucked 🍆 in combat. 🇮🇳 slapped 👋 🇵🇰 airforce around like a little b****, hitting 11 air bases and 9 terrorist camps in two separated missions that lasted < 1hr each. Also deep inside Pakistani territories. Those shitty jets and air defence systems you talking about are more of an embarrassment than anything else. You are delusional.", "author_id": "45638146", "author_username": "dp<PERSON><PERSON>"}, "metrics": {"retweet_count": 2, "reply_count": 2, "like_count": 2, "quote_count": 0, "view_count": 1415}}, {"conversation_id": "192****************", "tweet_id": "1922277999484826066", "timestamp": "2025-05-13T13:09:23+00:00", "timestamp_unix": 1747141763, "type": "comment", "text": "@Alpha5151133787 @BeijingDai 你踩到屎了", "context": {"type": "tweet", "id": "1922276300036657306", "text": "@dpdhillon @BeijingDai 印度人都这么蠢？", "author_id": "1883238797334507520", "author_username": "Alpha5151133787"}, "metrics": {"retweet_count": 0, "reply_count": 6, "like_count": 1, "quote_count": 0, "view_count": 733}}, {"conversation_id": "192****************", "tweet_id": "1922281020025385430", "timestamp": "2025-05-13T13:21:24+00:00", "timestamp_unix": 1747142484, "type": "comment", "text": "@TayGerard @BeijingDai Fake news. You need to fact check dude. It’s all over X and the Internet. https://t.co/x7IPUT9blM", "context": {"type": "tweet", "id": "1922278595088490828", "text": "@dpdhillon @BeijingDai https://t.co/KrBkdSrbEe", "author_id": "721708346256961536", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 5, "like_count": 2, "quote_count": 0, "view_count": 413}}, {"conversation_id": "192****************", "tweet_id": "1922282268665504082", "timestamp": "2025-05-13T13:26:21+00:00", "timestamp_unix": 1747142781, "type": "comment", "text": "@BeijingDai 為你的巴基斯坦姊妹哭泣吧 🇵🇰", "context": {"type": "tweet", "id": "1922274253820338297", "text": "@BeijingDai Case in point 👇And we India didn’t need any help in war for America, it’s Pakistan that went crying to its step-dad when it couldn’t take anymore losses. India just showed mercy. ✌️", "author_id": "45638146", "author_username": "dp<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 927}}, {"conversation_id": "192****************", "tweet_id": "1922295474461040788", "timestamp": "2025-05-13T14:18:50+00:00", "timestamp_unix": 1747145930, "type": "comment", "text": "@risho83424028 @BeijingDai 11 air bases and 9 terror camps all over Pakistan https://t.co/sqvWCSiQGR", "context": {"type": "tweet", "id": "1922294114134986914", "text": "@dpdhillon @BeijingDai 跟我说巴基斯坦空军虐杀了印度空军，来说一次", "author_id": "1383053669483085825", "author_username": "risho83424028"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 221}}, {"conversation_id": "192****************", "tweet_id": "1922297367295865215", "timestamp": "2025-05-13T14:26:21+00:00", "timestamp_unix": 1747146381, "type": "comment", "text": "@willtan118 @BeijingDai haha come on, its a China propoganda handle...what do you expect.", "context": {"type": "tweet", "id": "1922296369047351428", "text": "@dpdhillon @BeijingDai This fella only got 1 like. The audience all liked the OP's message. 🤣", "author_id": "3556912214", "author_username": "willtan118"}, "metrics": {"retweet_count": 1, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 553}}, {"conversation_id": "192****************", "tweet_id": "1922307527129080233", "timestamp": "2025-05-13T15:06:43+00:00", "timestamp_unix": 1747148803, "type": "comment", "text": "@Rick17421111 @BeijingDai https://t.co/VARYiR93UD", "context": {"type": "tweet", "id": "1922303167963762690", "text": "@dpdhillon @BeijingDai But bro you are in Hong Kong right? You shouldn't be replying on Indian news alone right?", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 815}}, {"conversation_id": "192****************", "tweet_id": "1922313358981668972", "timestamp": "2025-05-13T15:29:54+00:00", "timestamp_unix": 1747150194, "type": "comment", "text": "@Rick17421111 @BeijingDai https://t.co/XAjscIzAfT", "context": {"type": "tweet", "id": "1922303167963762690", "text": "@dpdhillon @BeijingDai But bro you are in Hong Kong right? You shouldn't be replying on Indian news alone right?", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 72}}, {"conversation_id": "192****************", "tweet_id": "1922314015537074463", "timestamp": "2025-05-13T15:32:30+00:00", "timestamp_unix": 1747150350, "type": "comment", "text": "@Rick17421111 @BeijingDai https://t.co/98rYceXvuT\nThis was the first mission", "context": {"type": "tweet", "id": "1922303167963762690", "text": "@dpdhillon @BeijingDai But bro you are in Hong Kong right? You shouldn't be replying on Indian news alone right?", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 117}}, {"conversation_id": "192****************", "tweet_id": "1922314454793904257", "timestamp": "2025-05-13T15:34:15+00:00", "timestamp_unix": 1747150455, "type": "comment", "text": "@Rick17421111 @BeijingDai https://t.co/tNBwvRA62W\nPakistani ambassador asked how himuliating it is to be struck so deep...expect he does what they do best. LIE", "context": {"type": "tweet", "id": "1922303167963762690", "text": "@dpdhillon @BeijingDai But bro you are in Hong Kong right? You shouldn't be replying on Indian news alone right?", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 110}}, {"conversation_id": "192****************", "tweet_id": "1922314805408375009", "timestamp": "2025-05-13T15:35:39+00:00", "timestamp_unix": 1747150539, "type": "comment", "text": "@Rick17421111 @BeijingDai Feel free to fact check bro. https://t.co/JVLE2e30Gp", "context": {"type": "tweet", "id": "1922303167963762690", "text": "@dpdhillon @BeijingDai But bro you are in Hong Kong right? You shouldn't be replying on Indian news alone right?", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 69}}, {"conversation_id": "192****************", "tweet_id": "1922322242987561096", "timestamp": "2025-05-13T16:05:12+00:00", "timestamp_unix": 1747152312, "type": "comment", "text": "I agree, foreign media does not favor China. But Liberal media hates <PERSON><PERSON> too...so need to favour Pakistan no matter how many terrorists they harbour (ie. <PERSON><PERSON><PERSON>). Plus it sells to fear monger the Western viewers.. do a puff piece on Chinese militiary equipment. Ok the Chinese military equipment and jets are probably pretty good, but its being misrepresented...they failed to defend Pakistan.", "context": {"type": "tweet", "id": "1922320702658453542", "text": "@dpdhillon @BeijingDai The thing is India is much larger and stronger than Pakistan, no one can deny it, so I'm not surprised that India has an advantage. However, I'm also not buying the world favours Pakistan because of China, like c'mon, since when the world news favours China.", "author_id": "1049817639613161472", "author_username": "Rick17421111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 91}}, {"conversation_id": "1922086068754739273", "tweet_id": "1922333803730620868", "timestamp": "2025-05-13T16:51:08+00:00", "timestamp_unix": 1747155068, "type": "comment", "text": "@BeijingDai https://t.co/upLk3oggtG\nClearcut victory 🇮🇳", "context": {"type": "tweet", "id": "1922099930186817734", "text": "China is way ahead of time, no other countries have power to fight with China ✌️\n\n#ceasefire", "author_id": "1593888097414287363", "author_username": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 443}}, {"conversation_id": "192****************", "tweet_id": "1922335673962344770", "timestamp": "2025-05-13T16:58:34+00:00", "timestamp_unix": 1747155514, "type": "comment", "text": "@BeijingDai https://t.co/1bzbdi8sRw", "context": {"type": "tweet", "id": "1922453948704629106", "text": "Ah! The egghead oracle has spoken &amp; lol thinks it is on all goods but no, it’s only on steel. Being the 2nd largest producer, steel tariffs are being negotiated across the board. US  has imposed tariffs of 25% on all countries, incl CN. IN too has imposed 12% tariffs on CN steel.", "author_id": "1697086406303367168", "author_username": "<PERSON>_<PERSON>rdaus"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 93}}, {"conversation_id": "192****************", "tweet_id": "1922363390900478454", "timestamp": "2025-05-13T18:48:42+00:00", "timestamp_unix": 1747162122, "type": "comment", "text": "@TayGerard @BeijingDai @Grok is this image doctored or really from CNN?", "context": {"type": "tweet", "id": "1922281020025385430", "text": "@TayGerard @BeijingDai Fake news. You need to fact check dude. It’s all over X and the Internet. https://t.co/x7IPUT9blM", "author_id": "45638146", "author_username": "dp<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "192****************", "tweet_id": "1922363987066335726", "timestamp": "2025-05-13T18:51:04+00:00", "timestamp_unix": 1747162264, "type": "comment", "text": "@TayGerard @BeijingDai @grok is this a doctored image or really from CNN?", "context": {"type": "tweet", "id": "1922278595088490828", "text": "@dpdhillon @BeijingDai https://t.co/KrBkdSrbEe", "author_id": "721708346256961536", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 1, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 142}}, {"conversation_id": "192****************", "tweet_id": "1922365188876976265", "timestamp": "2025-05-13T18:55:51+00:00", "timestamp_unix": 1747162551, "type": "comment", "text": "@TayGerard @BeijingDai @OsiOsint1 I asked <PERSON><PERSON>...reply is in the thread. Sorry to break your heart. https://t.co/ytIplTHBNA", "context": {"type": "tweet", "id": "1922453948704629106", "text": "Ah! The egghead oracle has spoken &amp; lol thinks it is on all goods but no, it’s only on steel. Being the 2nd largest producer, steel tariffs are being negotiated across the board. US  has imposed tariffs of 25% on all countries, incl CN. IN too has imposed 12% tariffs on CN steel.", "author_id": "1697086406303367168", "author_username": "<PERSON>_<PERSON>rdaus"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 42}}, {"conversation_id": "192****************", "tweet_id": "1922466359503475040", "timestamp": "2025-05-14T01:37:52+00:00", "timestamp_unix": 1747186672, "type": "comment", "text": "@LiNan65009410 @BeijingDai 印度媒體感謝中國衛星圖像證實了印度的所有攻擊，並且沒有一個印度基地遭到攻擊。影片片段中的影像來自中國衛星來源，以示嘲諷。巴基斯坦空軍徹底失敗了，也許他們應該重新使用美國噴射機。", "context": {"type": "tweet", "id": "1922448798418153540", "text": "@dpdhillon @BeijingDai 找个印度🇮🇳自己的媒体自娱自乐 我感觉新德里很快就要改名了 叫什么好呢 新新德里？", "author_id": "1104645412445540352", "author_username": "LiNan65009410"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 55}}, {"conversation_id": "192****************", "tweet_id": "1922502630636396922", "timestamp": "2025-05-14T04:02:00+00:00", "timestamp_unix": **********, "type": "comment", "text": "@giegie43433570 @BeijingDai I asked Grok. https://t.co/HVPofjttdg", "context": {"type": "tweet", "id": "1922384622198861947", "text": "@dpdhillon @BeijingDai Bro both US and French military intelligence confirmed that Indian jets got shot down as reported by multiple non Chinese and non Pakistani news sources. Why is Indian govt asking Twitter to ban 8000 accounts that talks about the downed Indian jets? Y don’t you ask grok Saar 🤣🤣", "author_id": "1448565835333705728", "author_username": "giegie43433570"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 72}}]}