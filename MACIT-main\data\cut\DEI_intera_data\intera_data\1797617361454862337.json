{"user_id": "1797617361454862337", "interactions_count": 24, "interactions": [{"conversation_id": "1919794038686814418", "tweet_id": "1919842510752412110", "timestamp": "2025-05-06T19:51:38+00:00", "timestamp_unix": 1746561098, "type": "comment", "text": "@IlGuiscardo7 @LegaSalvini Cioè tu scrivi un commento del genere e pretendi pure di parlare di “studi culturali”? Dai suvvia", "context": {"type": "tweet", "id": "1919819195098771751", "text": "@LegaSalvini Ma figuriamoci se la Lega possa essere d’accordo su studi culturali, non sareste in grado di capirli \nMolto meglio finanziare le gare di rutti, penso che 49 milioni bastino", "author_id": "1471544767989305345", "author_username": "IlGuiscardo7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1919794038686814418", "tweet_id": "1920013285845323998", "timestamp": "2025-05-07T07:10:14+00:00", "timestamp_unix": 1746601814, "type": "comment", "text": "@IlGuiscardo7 @LegaSalvini No guarda, la grammatica l’ho proprio tralasciata. Il problema è tutto il resto", "context": {"type": "tweet", "id": "1919857574804881489", "text": "@LucaMancus @LegaSalvini Per un errore di battitura? Addirittura 😂😂\nChiedo venia 😂😂😂 ma il concetto non cambia di una virgola", "author_id": "1471544767989305345", "author_username": "IlGuiscardo7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1920217338491576610", "tweet_id": "1920431573796614342", "timestamp": "2025-05-08T10:52:21+00:00", "timestamp_unix": 1746701541, "type": "comment", "text": "@marco21395854 @dscndt Pulizia etnica fatta molto male evidentemente visto h che è la popolazione palestinese è aumentata negli anni", "context": {"type": "tweet", "id": "1920227323296592370", "text": "@dscndt Ma veramente neghi la realtà? Ti invito a vedere la mostra itinerante, ora a <PERSON>eo, che spiega bene la pulizia etnica portata avanti da Israele contro i Palestinesi da decenni. E se ancora non ti basta, ti invito a parlare con un mio amico Palestinese, che vive in Cisgiordania.", "author_id": "1603503705571180545", "author_username": "marco21395854"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 48}}, {"conversation_id": "1921187306163101872", "tweet_id": "1921321595387150569", "timestamp": "2025-05-10T21:48:59+00:00", "timestamp_unix": 1746913739, "type": "comment", "text": "@Fortunablu17 No, è colpa dei negri", "context": {"type": "tweet", "id": "1921942613348790442", "text": "Domanda: ci crede davvero o cerca solo visibilità?", "author_id": "1207268171675324416", "author_username": "ParcodiGiacomo"}, "metrics": {"retweet_count": 2, "reply_count": 1, "like_count": 144, "quote_count": 0, "view_count": 3371}}, {"conversation_id": "1921167461862297728", "tweet_id": "1921526240525058523", "timestamp": "2025-05-11T11:22:10+00:00", "timestamp_unix": 1746962530, "type": "comment", "text": "@Leos715 @studisraele Poi prova ad andare a Gaza a dire, che so, basta ad Hamas che tiene come scudi umani dei civili innocenti per poter continuare la sua propaganda. Vediamo se tornate (vivi)", "context": {"type": "tweet", "id": "1924066871201964355", "text": "@roxgiuse @GregPignataro https://t.co/2Frh5iKHWv", "author_id": "449543043", "author_username": "fabcal1"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 5, "quote_count": 0, "view_count": 74}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921549273436496167", "timestamp": "2025-05-11T12:53:42+00:00", "timestamp_unix": 1746968022, "type": "comment", "text": "@Milton_Keynes1 @Axen0s Potreste dirmi cosa hanno detto di sbagliato esattamente?", "context": {"type": "tweet", "id": "1921537435655180761", "text": "@Axen0s C’è da dire che hai preso due eccellenze nostrane dell’idiozia. Gente se che se facessero le olimpiadi degli imbecilli darebbero lustro all’italia nei secoli.", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921596724335841756", "timestamp": "2025-05-11T16:02:15+00:00", "timestamp_unix": 1746979335, "type": "comment", "text": "@Milton_Keynes1 @Axen0s Entra nel merito: citami una cosa detta da Borghi che trovi essere sbagliata. Così ne parliamo meglio, altrimenti sembra ti abbia rubato la fidanzata e basta", "context": {"type": "tweet", "id": "1921581411946877174", "text": "@LucaMancus @Axen0s Nella loro vita? Più facile cercare di trovare qualcosa di giusto. Nulla.", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921938970641907894", "timestamp": "2025-05-12T14:42:13+00:00", "timestamp_unix": 1747060933, "type": "comment", "text": "@Milton_Keynes1 @Axen0s Eh si, do<PERSON><PERSON>. Perché mi hai citato cose abbastanza irrilevanti. Il chiedere come tagliare un prosciutto farebbe di lui che cosa? Bah", "context": {"type": "tweet", "id": "1921621941359644989", "text": "@LucaMancus @Axen0s Devo continuare?", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921939292340863256", "timestamp": "2025-05-12T14:43:29+00:00", "timestamp_unix": 1747061009, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Meravigliosamente falso. Borghi non ha confuso nulla, ha citato delle tabelle ministeriali. Il fatto che due numeri si somiglino non significa niente. Leggete il thread prima di fare figuracce", "context": {"type": "tweet", "id": "1921637355535974525", "text": "@Milton_Keynes1 @LucaMancus Ma questa è meravigliosa", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921955494991655332", "timestamp": "2025-05-12T15:47:52+00:00", "timestamp_unix": 1747064872, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Il punto è: confonde i litri con l’alcolemia? La risposta è NO! \nPoi era un post su X per far capire il senso della misura, non una prescrizione medica", "context": {"type": "tweet", "id": "1921948404042641632", "text": "@LucaMancus @Milton_Keynes1 Come fa a dire che non arriva a 0,8 se non sa quanto pesa e se non sa se è a stomaco pieno o vuoto?\nE soprattutto, se il tasso alcolemico è compreso tra 0,5 e 0,8 rischia tutta sta roba qui https://t.co/q7dwfKaq35", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921960286086439103", "timestamp": "2025-05-12T16:06:55+00:00", "timestamp_unix": 1747066015, "type": "comment", "text": "@Milton_Keynes1 @Axen0s Amico dico davvero, stai prendendo un abbaglio, <PERSON><PERSON><PERSON> non ha confuso nulla. <PERSON><PERSON><PERSON>, leggi il thread in questione", "context": {"type": "tweet", "id": "1921958751679975515", "text": "@LucaMancus @Axen0s 😂😂😂😂😂😂😂", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921962440104857665", "timestamp": "2025-05-12T16:15:28+00:00", "timestamp_unix": 1747066528, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Questa è la tabella #ISS: uomo, 75 kg, 125 cc di vino = 0,14 g/L. Per fare 0,84 g/L, dovresti bere una bottiglia intera. Metabolismo medio: (0,15 g/L/h). Il valore scenderebbe sotto 0,8 in 15 minuti. \nCosa non ti torna di ciò che ha scritto Borghi? Ripeto: rileggiti il thread https://t.co/Yysc40KyCF", "context": {"type": "tweet", "id": "1921958614723367274", "text": "@LucaMancus @Milton_Keynes1 La risposta è sì visto che non accenna a nessun altro parametro", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921962604181733712", "timestamp": "2025-05-12T16:16:07+00:00", "timestamp_unix": 1747066567, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Io non devo essere sicuro, sono parametri iss, se hai dubbi chiedi a loro", "context": {"type": "tweet", "id": "1921960875843350983", "text": "@LucaMancus @Milton_Keynes1 Ok, ma se io bevo una bottiglia di prosecco o se sono emiliano e me ne solo una di lambrusco sei sicuro che non arrivo a 0,8?", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921982498784886795", "timestamp": "2025-05-12T17:35:11+00:00", "timestamp_unix": 1747071311, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Dice che non è 1 litro ma 0,75. Non c’entra niente con il tasso alcolemico. Borg<PERSON> ha riportato solo i valori da tabella e niente altro", "context": {"type": "tweet", "id": "1921968145499074620", "text": "@LucaMancus @Milton_Keynes1 Appunto. Se ne bevi una intera arrivi a 0,84. Ma lui dice che non arrivi a 0,8 perché una bottiglia intera è 0,75 l\n\nQuindi è chiaro a cosa si riferisce", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921982919742026105", "timestamp": "2025-05-12T17:36:51+00:00", "timestamp_unix": 1747071411, "type": "comment", "text": "@Milton_Keynes1 @Axen0s Con l’affettatrice voleva tagliare la cotenna… ma al netto di questo esiste qualche argomentazione nel merito o no?", "context": {"type": "tweet", "id": "1921976461977665714", "text": "@LucaMancus @Axen0s Voler tagliare l’osso con l’affettatrice è abbastanza da coglioni. Pensa i suoi avvocat li difensori sul tuiter cosa sono…..", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921989970312446316", "timestamp": "2025-05-12T18:04:52+00:00", "timestamp_unix": 1747073092, "type": "comment", "text": "@Axen0s @Milton_Keynes1 E ha ragione! Come la tabella iss dimostra. Quale sarebbe il problema?😂", "context": {"type": "tweet", "id": "1921984195317936498", "text": "@LucaMancus @Milton_Keynes1 Dice che se ti scoli una bottiglia non arrivi a 0,8 perché è 0,75", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921992611646103992", "timestamp": "2025-05-12T18:15:22+00:00", "timestamp_unix": 1747073722, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Io ti ho fatto l’esempio per 75kg, se pesi di più stai comunque sotto. Ed in ogni caso Borghi NON ha confuso i litri della bottiglia con l’alcolemia, come dimostra il thread e la tabella iss. Questo direi che chiude la questione", "context": {"type": "tweet", "id": "1921991761313800502", "text": "@LucaMancus @Milton_Keynes1 Se ti scoli una bottiglia arrivi a 0,84 (lo hai scritto tu stesso) e ti arrestano", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921993600520351912", "timestamp": "2025-05-12T18:19:17+00:00", "timestamp_unix": 1747073957, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Il tuo si. Tanto per chiudere del tutto, questa è la continuazione del messaggio di Borghi (che potete leggere nel thread che non avete letto) https://t.co/cAMamgOREc", "context": {"type": "tweet", "id": "1921993235997622770", "text": "@LucaMancus @Milton_Keynes1 Questo ragionamento tecnicamente si chiama cherry picking", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921996120437256305", "timestamp": "2025-05-12T18:29:18+00:00", "timestamp_unix": 1747074558, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Si<PERSON> Quindi?", "context": {"type": "tweet", "id": "1921994870320742861", "text": "@LucaMancus @Milton_Keynes1 0, 78  sono 2000 euri di multa, sequestro del mezzo e dieci punti in meno sulla patente", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921997996322914536", "timestamp": "2025-05-12T18:36:45+00:00", "timestamp_unix": 1747075005, "type": "comment", "text": "@Axen0s @Milton_Keynes1 Quindi <PERSON> non ha confuso i litri di vino col tasso alcolemico, giusto? Almeno questo riesci a dirlo?", "context": {"type": "tweet", "id": "1921997306179547359", "text": "@LucaMancus @Milton_Keynes1 Quindi puoi bere una bottiglia di vino, tanto è 0,75", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921480965496426821", "tweet_id": "1922001981784105079", "timestamp": "2025-05-12T18:52:36+00:00", "timestamp_unix": 1747075956, "type": "comment", "text": "@Axen0s <PERSON>tto, grazie per aver giocato", "context": {"type": "tweet", "id": "1921999900478218738", "text": "@LucaMancus No.", "author_id": "384788314", "author_username": "Axen0s"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1921244020178641260", "tweet_id": "1922221481787859228", "timestamp": "2025-05-13T09:24:49+00:00", "timestamp_unix": 1747128289, "type": "comment", "text": "@CianoArrabiziti @RiccardoTrezzi @EmanueleBracco Le lungaggini burocratiche pagate da persone sicuramente per bene come la signora Trezzi sono un piccolo prezzo che devono pagare in un disegno più grande.", "context": {"type": "tweet", "id": "1922048201474888114", "text": "@RiccardoTrezzi @EmanueleBracco <PERSON>, anche per questo ha senso ridurlo a cinque (che poi sarebbero 7/8). Ovviamente non solo per questo e non dovrebbe essere questa la soluzione purtroppo bisogna essere concreti o non si risolve nessun inconveniente.", "author_id": "728675745934000128", "author_username": "CianoArrabiziti"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1922407397823226318", "tweet_id": "1922592145933824304", "timestamp": "2025-05-14T09:57:42+00:00", "timestamp_unix": 1747216662, "type": "comment", "text": "@loziovale71 @AS74900963 @CartabiancaR4 Beh, in realtà si. Germano è strapagato non perché è bravo ma perché prende soldi pubblici. Ricordo che ha lasciato un buco di bilancio di 3 milioni di euro proprio perché la gente non apprezza i suoi lavori", "context": {"type": "tweet", "id": "1922590997306912975", "text": "@AS74900963 @CartabiancaR4 \"Il film fa schifo\" e \"il pubblico non va a vederlo\" non sono frasi equivalenti.", "author_id": "1587765882838712321", "author_username": "loziovale71"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1922407397823226318", "tweet_id": "1922592336443240794", "timestamp": "2025-05-14T09:58:27+00:00", "timestamp_unix": 1747216707, "type": "comment", "text": "@polloman75 @AS74900963 @CartabiancaR4 Perché i politici vengono votati ed eletti, ti piacciano o meno, i registi no.", "context": {"type": "tweet", "id": "1923076964287414382", "text": "👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏👏", "author_id": "782497652781047809", "author_username": "Stefano25158568"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}]}