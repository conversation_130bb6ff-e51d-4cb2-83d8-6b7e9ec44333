@echo off
echo ========================================
echo DEI数据处理脚本
echo 使用MACIT框架处理DEI_intera_data数据
echo ========================================

REM 激活anaconda环境
echo 正在激活MACIT虚拟环境...
call conda activate MACIT

REM 检查环境是否激活成功
if errorlevel 1 (
    echo 错误：无法激活MACIT虚拟环境
    echo 请确保已创建MACIT虚拟环境
    pause
    exit /b 1
)

echo MACIT虚拟环境已激活

REM 切换到项目目录
cd /d "%~dp0"

REM 检查Python脚本是否存在
if not exist "process_dei_data.py" (
    echo 错误：找不到process_dei_data.py文件
    pause
    exit /b 1
)

REM 检查DEI数据目录是否存在
if not exist "data\cut\DEI_intera_data\intera_data" (
    echo 错误：找不到DEI数据目录
    echo 请确保DEI数据位于: data\cut\DEI_intera_data\intera_data
    pause
    exit /b 1
)

REM 创建DEI_output目录（如果不存在）
if not exist "DEI_output" (
    echo 创建DEI_output目录...
    mkdir DEI_output
)

echo ========================================
echo 开始处理DEI数据...
echo ========================================

REM 运行DEI数据处理脚本
python process_dei_data.py --max_users 10

REM 检查执行结果
if errorlevel 1 (
    echo ========================================
    echo DEI数据处理过程中出现错误
    echo ========================================
) else (
    echo ========================================
    echo DEI数据处理完成！
    echo 结果已保存到DEI_output文件夹
    echo ========================================
)

pause
