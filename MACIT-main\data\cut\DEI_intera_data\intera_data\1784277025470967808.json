{"user_id": "1784277025470967808", "interactions_count": 18, "interactions": [{"conversation_id": "1922850629078126692", "tweet_id": "1922852243998437695", "timestamp": "2025-05-15T03:11:14+00:00", "timestamp_unix": 1747278674, "type": "comment", "text": "@Reform_West @mmpadellan What does a pardon have to do with <PERSON><PERSON><PERSON> being released? He was convicted in Minnesota State court. A pardon means nothing.", "context": {"type": "tweet", "id": "1922851156578939251", "text": "@mmpadellan Release Chauvin.\n\n<PERSON> died of fentanyl overdose.", "author_id": "3258760843", "author_username": "Reform_West"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 8, "quote_count": 0, "view_count": 129}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922852424013803743", "timestamp": "2025-05-15T03:11:57+00:00", "timestamp_unix": 1747278717, "type": "comment", "text": "@RealLifeFootage @mmpadellan What's the broken system? That people convicted unanimously by juries serve their sentences?", "context": {"type": "tweet", "id": "1922851701955903841", "text": "@mmpadellan BrooklynDad just exposed the left’s real game... they don’t want justice—they want division. President <PERSON> stands for law and order, not race-baiting. This isn’t about “giving the finger”—it’s about fixing a broken system.", "author_id": "1599030478727614465", "author_username": "RealLifeFootage"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 4, "quote_count": 0, "view_count": 69}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922852597414731924", "timestamp": "2025-05-15T03:12:38+00:00", "timestamp_unix": 1747278758, "type": "comment", "text": "@TuthSophia @mmpadellan Can you link to the story about <PERSON><PERSON> calling for violence?", "context": {"type": "tweet", "id": "1922851950212596004", "text": "@mmpadellan <PERSON> should be released BECAUSE of the jury intimidation brought forth by <PERSON><PERSON> and her call for more violence  DURING the trial...\n\nShe would be in jail not <PERSON>", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922853431485550728", "timestamp": "2025-05-15T03:15:57+00:00", "timestamp_unix": 1747278957, "type": "comment", "text": "@TuthSophia @mmpadellan \"We have to stay on the street. We have to get more confrontational. We have to let them know we mean business.\"\n\nI asked for the call for violence. This is not a call for violence.", "context": {"type": "tweet", "id": "1922852883411640808", "text": "@ElonMuskSucks97 @mmpadellan https://t.co/Oc1echsNZD", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922853608346857648", "timestamp": "2025-05-15T03:16:39+00:00", "timestamp_unix": 1747278999, "type": "comment", "text": "@TuthSophia @mmpadellan See previous comment. I realize that you have, over a series of years, referred to this as a call for violence so many times, you likely actually believe it...but this is not a call for violence.", "context": {"type": "tweet", "id": "1922853363361693897", "text": "@ElonMuskSucks97 @mmpadellan https://t.co/dQWsMhJGgc", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922855154304368909", "timestamp": "2025-05-15T03:22:48+00:00", "timestamp_unix": 1747279368, "type": "comment", "text": "You are free to infer anything you like. But you mentioned that the <PERSON>woman should be in jail., and there is no call for violence here. Protest is not supposed to be polite. It is, by its nature, supposed to be confrontational, raucous, and unpleasant. But not violent...and she did not suggest violence.", "context": {"type": "tweet", "id": "1922854278458159517", "text": "@ElonMuskSucks97 @mmpadellan Ah yes the CODED words... BLM riots of INNOCENT KILLINGS..\n\nMore confrontational  UNLESS ITS GUILTY GUILTY GUILTY...\n\nHow do you get more confrontational  when there have been lives taken from the BLM killings?", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922855255726768269", "timestamp": "2025-05-15T03:23:12+00:00", "timestamp_unix": 1747279392, "type": "comment", "text": "@TuthSophia @mmpadellan What person died because they wore a red hat?", "context": {"type": "tweet", "id": "1922854501146353895", "text": "@ElonMuskSucks97 @mmpadellan \"More confrontational\" means what? Remaining in the streets that has cause billions of dollars in damages, innocent lives taken because they wore a red hat", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922855547000205519", "timestamp": "2025-05-15T03:24:21+00:00", "timestamp_unix": 1747279461, "type": "comment", "text": "@TuthSophia @mmpadellan I am not an idiot, and I will not engage in any conversation based on that premise. Pose a new premise, and I would be happy to consider your question.", "context": {"type": "tweet", "id": "1923201464014434304", "text": "Blacks for Trump are either the dumbest fucks around or are suffering from the Uncle Tom syndrome!", "author_id": "********", "author_username": "NoraBrup"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922857260495081528", "timestamp": "2025-05-15T03:31:10+00:00", "timestamp_unix": **********, "type": "comment", "text": "@TuthSophia @mmpadellan I don't doubt that at all. The question was \"who was killed because they wore a red hat?\", since that was your claim.", "context": {"type": "tweet", "id": "1922856805312422287", "text": "@ElonMuskSucks97 @mmpadellan People killed: In early June, news accounts reported the number of people killed during the Floyd protests at roughly a dozen, or as many as 19. The victims include a 77-year-old man who was a retired St. Louis police captain and a 22-year-old woman from Davenport, Iowa.", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922857505408905500", "timestamp": "2025-05-15T03:32:08+00:00", "timestamp_unix": **********, "type": "comment", "text": "@TuthSophia @mmpadellan I disagree and I 100% reject your premise.", "context": {"type": "tweet", "id": "1923201464014434304", "text": "Blacks for Trump are either the dumbest fucks around or are suffering from the Uncle Tom syndrome!", "author_id": "********", "author_username": "NoraBrup"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922861773016670393", "timestamp": "2025-05-15T03:49:06+00:00", "timestamp_unix": 1747280946, "type": "comment", "text": "@TuthSophia @mmpadellan I'm certain that it was. Still does not address my question. Your claim was that people died because they wore a red hat. Who does this refer to?", "context": {"type": "tweet", "id": "1922861519940866391", "text": "@ElonMuskSucks97 @mmpadellan The video was being spread all over twitter and facebook.", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 1, "view_count": 10}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922862355001614760", "timestamp": "2025-05-15T03:51:25+00:00", "timestamp_unix": 1747281085, "type": "comment", "text": "So...did somebody die because of their hat or not? If so, who was that person? This is the fourth attempt to get an answer to this question, and just so you know, we idiots are known to for single track minds, so until there's an answer to this question, there's not going to be any others.", "context": {"type": "tweet", "id": "1922861880403546287", "text": "@ElonMuskSucks97 @mmpadellan @grok black teens targeted people wearing a red hat, video spread all over Facebook and Twitter, \"there's one over there\".", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922864527210668413", "timestamp": "2025-05-15T04:00:03+00:00", "timestamp_unix": 1747281603, "type": "comment", "text": "If they've been working overtime to scrub that shit, then it's either out there for you to find...or it's not out there. It's not just a little out there so that I have to do the work but you don't. If you know that somebody died because of a red hat, what's their name? Show your work.", "context": {"type": "tweet", "id": "1923201464014434304", "text": "Blacks for Trump are either the dumbest fucks around or are suffering from the Uncle Tom syndrome!", "author_id": "********", "author_username": "NoraBrup"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1922850629078126692", "tweet_id": "1922877356693148005", "timestamp": "2025-05-15T04:51:01+00:00", "timestamp_unix": 1747284661, "type": "comment", "text": "@TuthSophia @mmpadellan @grok This is the victim, and the hat he was wearing at the time. https://t.co/jaCT6ebuVv", "context": {"type": "tweet", "id": "1922875801512325262", "text": "@ElonMuskSucks97 @mmpadellan @grok https://t.co/6vR9UL2Ohb", "author_id": "1519776531635187713", "author_username": "TuthSophia"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1924454820091457794", "tweet_id": "1924479830730363067", "timestamp": "2025-05-19T14:58:41+00:00", "timestamp_unix": 1747666721, "type": "comment", "text": "My points are never \"based on\" one particular language or another. Do you find you're making points in English that make no sense in a different language?\n\nA mass escape from confinement is, by definition, a failure. If you want to blame this on skin color or gender, it's on you to explain how those things contributed to the problem. What do ya got?", "context": {"type": "tweet", "id": "1924481411081474359", "text": "Losers in LA", "author_id": "1544097193", "author_username": "rlviz"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 2, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1924454820091457794", "tweet_id": "1924507377459404884", "timestamp": "2025-05-19T16:48:09+00:00", "timestamp_unix": 1747673289, "type": "comment", "text": "@snakeflag @MattWalshBlog I'm not familiar with the case. I haven't read up on it. I simply know that by the time you're remotely in charge at a PRISON, you probably understand that locking doors is a good thing. I'd be happy to hear your theory of how gender or race contributed to this situation.", "context": {"type": "tweet", "id": "1924506842404618380", "text": "@ElonMuskSucks97 @MattWalshBlog What are all the things BESIDES their gender that contributed to their colossal failure? Please be specific and cite sources.", "author_id": "1051184094032187393", "author_username": "snakeflag"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1924454820091457794", "tweet_id": "1924518754718712224", "timestamp": "2025-05-19T17:33:21+00:00", "timestamp_unix": 1747676001, "type": "comment", "text": "@snakeflag @MattWalshBlog No. It's not that interesting to me. If you have evidence that you're right, present it. That's the outer extent of my caring about a particular case. But I will ALWAYS make this challenge to someone claiming DEI was the cause of anything.", "context": {"type": "tweet", "id": "1924518031696293904", "text": "@ElonMuskSucks97 @MattWalshBlog Cool! Read up on it, and get back to me 👍", "author_id": "1051184094032187393", "author_username": "snakeflag"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1924454820091457794", "tweet_id": "1924623360979279987", "timestamp": "2025-05-20T00:29:01+00:00", "timestamp_unix": 1747700941, "type": "comment", "text": "@snakeflag @MattWalshBlog I don’t have it. A lack of evidence doesn’t mean the wild theory is correct. Surely you’ve encountered the phrase extraordinary theories require extra extraordinary evidence?” It’s not on me to prove what everybody else assumes is correct actually is.", "context": {"type": "tweet", "id": "1924619083120582956", "text": "@ElonMuskSucks97 @MattWalshBlog If you had evidence I'm wrong, that would have been a good time to present it. But, I'll meet you halfway. It probably wasn't just a race and gender issue; the fact that they're retarded leftists should get a fair share of the blame, too 🤷🏿‍♂️", "author_id": "1051184094032187393", "author_username": "snakeflag"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}]}