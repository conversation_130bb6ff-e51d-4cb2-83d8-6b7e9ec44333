#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import logging
from macit_framework import MACITFramework

def main():
    """
    测试单个用户的意图分析
    """
    if len(sys.argv) < 2:
        print("使用方法: python test_single_user.py <user_id>")
        return
    
    user_id = sys.argv[1]
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("test_single_user")
    
    logger.info(f"开始测试用户 {user_id} 的意图分析...")
    
    try:
        # 初始化MACIT框架
        macit = MACITFramework()
        
        # 分析用户意图
        logger.info(f"正在分析用户 {user_id}...")
        result = macit.analyze_user_intent(user_id)
        
        if result:
            # 保存结果
            output_path = macit.save_analysis_result(result, user_id, 0)
            logger.info(f"用户 {user_id} 分析完成！结果保存在: {output_path}")
        else:
            logger.error(f"用户 {user_id} 分析失败：未获得有效结果")
        
    except Exception as e:
        logger.error(f"分析用户 {user_id} 时出错: {e}")

if __name__ == "__main__":
    main() 