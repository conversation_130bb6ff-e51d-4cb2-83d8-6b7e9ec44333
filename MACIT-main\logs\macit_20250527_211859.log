2025-05-27 21:19:02,440 - test_10_users - INFO - 开始测试10个用户的意图分析...
2025-05-27 21:19:02,461 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:19:02,461 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:19:02,462 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:19:02,462 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:19:02,462 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:02,462 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:02,463 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:02,463 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:03,095 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:03,095 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:03,736 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:19:03,736 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:19:03,737 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:03,737 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:03,737 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:03,737 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:04,242 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,242 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,243 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:19:04,243 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:19:04,243 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,243 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,244 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:04,244 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:04,765 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,765 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,766 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:19:04,766 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:19:04,767 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,767 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:04,767 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:04,767 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:05,302 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:05,302 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:05,304 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:19:05,304 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:19:05,304 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:19:05,304 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:19:05,305 - test_10_users - INFO - MACIT框架初始化成功
2025-05-27 21:19:05,307 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:19:05,307 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:19:05,308 - test_10_users - INFO - 将测试以下10个用户: ['1004599646080131072', '1021702922927669249', '1027405890314358784', '1034308064', '1037663718686031879', '1038128766575202306', '1058187164', '1064220744677232642', '1070439999517024257', '1070616509423280128']
2025-05-27 21:19:05,308 - test_10_users - INFO - 正在分析用户 1004599646080131072 (1/10)...
2025-05-27 21:19:05,308 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,308 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,310 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:05,310 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:05,310 - macit_framework - WARNING - 用户 1004599646080131072 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,310 - macit_framework - WARNING - 用户 1004599646080131072 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,311 - test_10_users - ERROR - 分析用户 1004599646080131072 时出错: 上下文内容安全检查失败: 1004599646080131072
2025-05-27 21:19:05,311 - test_10_users - INFO - 正在分析用户 1021702922927669249 (2/10)...
2025-05-27 21:19:05,312 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,312 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,313 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:05,313 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:05,313 - macit_framework - WARNING - 用户 1021702922927669249 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,313 - macit_framework - WARNING - 用户 1021702922927669249 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,314 - test_10_users - ERROR - 分析用户 1021702922927669249 时出错: 上下文内容安全检查失败: 1021702922927669249
2025-05-27 21:19:05,315 - test_10_users - INFO - 正在分析用户 1027405890314358784 (3/10)...
2025-05-27 21:19:05,316 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,316 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,317 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:05,317 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:05,318 - macit_framework - WARNING - 用户 1027405890314358784 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,318 - macit_framework - WARNING - 用户 1027405890314358784 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,319 - test_10_users - ERROR - 分析用户 1027405890314358784 时出错: 上下文内容安全检查失败: 1027405890314358784
2025-05-27 21:19:05,319 - test_10_users - INFO - 正在分析用户 1034308064 (4/10)...
2025-05-27 21:19:05,321 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,321 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,322 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:05,322 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:05,322 - macit_framework - WARNING - 用户 1034308064 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,322 - macit_framework - WARNING - 用户 1034308064 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,323 - test_10_users - ERROR - 分析用户 1034308064 时出错: 上下文内容安全检查失败: 1034308064
2025-05-27 21:19:05,324 - test_10_users - INFO - 正在分析用户 1037663718686031879 (5/10)...
2025-05-27 21:19:05,324 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,324 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,325 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:05,325 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:05,326 - macit_framework - WARNING - 用户 1037663718686031879 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,326 - macit_framework - WARNING - 用户 1037663718686031879 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,327 - test_10_users - ERROR - 分析用户 1037663718686031879 时出错: 上下文内容安全检查失败: 1037663718686031879
2025-05-27 21:19:05,327 - test_10_users - INFO - 正在分析用户 1038128766575202306 (6/10)...
2025-05-27 21:19:05,328 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,328 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,329 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:05,329 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:05,329 - macit_framework - WARNING - 用户 1038128766575202306 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,329 - macit_framework - WARNING - 用户 1038128766575202306 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,330 - test_10_users - ERROR - 分析用户 1038128766575202306 时出错: 上下文内容安全检查失败: 1038128766575202306
2025-05-27 21:19:05,330 - test_10_users - INFO - 正在分析用户 1058187164 (7/10)...
2025-05-27 21:19:05,331 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,331 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,333 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:05,333 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:05,333 - macit_framework - WARNING - 用户 1058187164 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,333 - macit_framework - WARNING - 用户 1058187164 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,334 - test_10_users - ERROR - 分析用户 1058187164 时出错: 上下文内容安全检查失败: 1058187164
2025-05-27 21:19:05,335 - test_10_users - INFO - 正在分析用户 1064220744677232642 (8/10)...
2025-05-27 21:19:05,335 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,335 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,337 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:05,337 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:05,337 - macit_framework - WARNING - 用户 1064220744677232642 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,337 - macit_framework - WARNING - 用户 1064220744677232642 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,338 - test_10_users - ERROR - 分析用户 1064220744677232642 时出错: 上下文内容安全检查失败: 1064220744677232642
2025-05-27 21:19:05,339 - test_10_users - INFO - 正在分析用户 1070439999517024257 (9/10)...
2025-05-27 21:19:05,340 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,340 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,341 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:05,341 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:05,341 - macit_framework - WARNING - 用户 1070439999517024257 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,341 - macit_framework - WARNING - 用户 1070439999517024257 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,342 - test_10_users - ERROR - 分析用户 1070439999517024257 时出错: 上下文内容安全检查失败: 1070439999517024257
2025-05-27 21:19:05,342 - test_10_users - INFO - 正在分析用户 1070616509423280128 (10/10)...
2025-05-27 21:19:05,343 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,343 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:05,345 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:05,345 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:05,345 - macit_framework - WARNING - 用户 1070616509423280128 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,345 - macit_framework - WARNING - 用户 1070616509423280128 的上下文内容可能存在风险，跳过分析
2025-05-27 21:19:05,346 - test_10_users - ERROR - 分析用户 1070616509423280128 时出错: 上下文内容安全检查失败: 1070616509423280128
2025-05-27 21:19:05,346 - test_10_users - INFO - 所有用户分析完成，正在整理结果...
2025-05-27 21:19:05,348 - test_10_users - INFO - 格式化结果已保存至: output\test_10_users_results.txt
