#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行所有未处理的用户数据
"""

import argparse
import logging
import sys
import time
from pathlib import Path

from logger_config import setup_logger
from macit_framework import MACITFramework

# 设置日志
logger = setup_logger('run_unprocessed')


def get_unprocessed_users():
    """
    获取所有未处理的用户ID

    Returns:
        list: 未处理的用户ID列表
    """
    # 数据目录和输出目录
    base_dir = Path(__file__).parent
    data_dir = base_dir / "data/cut/intera_data"
    output_dir = base_dir / "output"

    # 获取所有数据文件
    all_data_files = list(data_dir.glob("*.json"))
    all_user_ids = [f.stem for f in all_data_files]

    # 获取已处理的用户ID
    processed_user_ids = []
    if output_dir.exists():
        for user_dir in output_dir.iterdir():
            if user_dir.is_dir():
                # 检查是否有interaction_0.json文件
                interaction_file = user_dir / "interaction_0.json"
                if interaction_file.exists():
                    processed_user_ids.append(user_dir.name)

    # 找出未处理的用户ID
    unprocessed_user_ids = [uid for uid in all_user_ids if uid not in processed_user_ids]

    logger.info(f"总用户数: {len(all_user_ids)}")
    logger.info(f"已处理用户数: {len(processed_user_ids)}")
    logger.info(f"未处理用户数: {len(unprocessed_user_ids)}")

    return unprocessed_user_ids


def main():
    """主函数"""
    logger.info("开始运行所有未处理的用户数据")

    # 获取未处理的用户列表
    unprocessed_users = get_unprocessed_users()

    if not unprocessed_users:
        logger.info("没有未处理的用户数据")
        return

    # 创建MACIT框架
    logger.info("正在初始化MACIT框架（使用DeepSeek-chat模型）...")
    framework = MACITFramework()
    logger.info("MACIT框架初始化完成，包含4个智能体")

    logger.info(f"将要分析 {len(unprocessed_users)} 个未处理用户的细粒度结构化意图")

    # 分析每个用户的意图
    successful_analyses = 0
    failed_analyses = 0

    for i, user_id in enumerate(unprocessed_users, 1):
        logger.info(f"正在分析用户 {user_id} ({i}/{len(unprocessed_users)})...")

        try:
            # 分析用户意图
            result = framework.analyze_user_intent(
                user_id=user_id,
                interaction_index=0,  # 默认分析第一个交互
                max_rounds=None,  # 使用配置文件中的默认值
            )

            # 保存分析结果
            output_path = framework.save_analysis_result(
                result=result,
                user_id=user_id,
                interaction_index=0,
            )

            logger.info(f"用户 {user_id} 的细粒度意图分析完成，结果保存到: {output_path}")
            successful_analyses += 1

            # 每处理10个用户输出一次进度
            if i % 10 == 0:
                logger.info(f"进度: {i}/{len(unprocessed_users)} ({i/len(unprocessed_users)*100:.1f}%)")
                logger.info(f"当前成功率: {successful_analyses/(successful_analyses+failed_analyses)*100:.1f}%")

        except ValueError as e:
            # 内容安全检查失败，跳过该用户
            logger.warning(f"跳过用户 {user_id}: {e}")
            failed_analyses += 1

        except Exception as e:
            # 检查是否是DeepSeek内容风险错误
            error_msg = str(e)
            if "Content Exists Risk" in error_msg or "Content Risk" in error_msg:
                logger.warning(f"用户 {user_id} 触发内容风险检查，跳过")
                failed_analyses += 1
            else:
                logger.error(f"分析用户 {user_id} 时出错: {e}")
                import traceback
                traceback.print_exc()
                failed_analyses += 1

        # 添加延迟，避免过快请求
        time.sleep(1)  # 减少延迟以加快处理速度

    # 输出最终统计
    logger.info("所有未处理用户的细粒度意图分析已完成")
    logger.info(f"统计结果: 成功分析 {successful_analyses} 个用户，失败 {failed_analyses} 个用户")
    logger.info(f"成功率: {successful_analyses/(successful_analyses+failed_analyses)*100:.1f}%")

    # 保存处理结果统计
    with open("processing_results.txt", "w", encoding="utf-8") as f:
        f.write(f"处理完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总处理用户数: {len(unprocessed_users)}\n")
        f.write(f"成功分析: {successful_analyses}\n")
        f.write(f"失败分析: {failed_analyses}\n")
        f.write(f"成功率: {successful_analyses/(successful_analyses+failed_analyses)*100:.1f}%\n")


if __name__ == "__main__":
    main()
