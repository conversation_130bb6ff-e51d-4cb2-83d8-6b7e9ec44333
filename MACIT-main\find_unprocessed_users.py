#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查找尚未处理的用户数据
"""

import os
from pathlib import Path

def find_unprocessed_users():
    """
    查找尚未处理的用户数据
    
    Returns:
        list: 未处理的用户ID列表
    """
    # 数据目录和输出目录
    data_dir = Path("data/cut/intera_data")
    output_dir = Path("output")
    
    # 获取所有数据文件
    all_data_files = list(data_dir.glob("*.json"))
    all_user_ids = [f.stem for f in all_data_files]
    
    # 获取已处理的用户ID
    processed_user_ids = []
    if output_dir.exists():
        for user_dir in output_dir.iterdir():
            if user_dir.is_dir():
                # 检查是否有interaction_0.json文件
                interaction_file = user_dir / "interaction_0.json"
                if interaction_file.exists():
                    processed_user_ids.append(user_dir.name)
    
    # 找出未处理的用户ID
    unprocessed_user_ids = [uid for uid in all_user_ids if uid not in processed_user_ids]
    
    print(f"总用户数: {len(all_user_ids)}")
    print(f"已处理用户数: {len(processed_user_ids)}")
    print(f"未处理用户数: {len(unprocessed_user_ids)}")
    
    print("\n已处理的用户ID:")
    for uid in sorted(processed_user_ids):
        print(f"  {uid}")
    
    print("\n未处理的用户ID:")
    for uid in sorted(unprocessed_user_ids):
        print(f"  {uid}")
    
    return unprocessed_user_ids

if __name__ == "__main__":
    unprocessed = find_unprocessed_users()
    
    # 保存未处理用户列表到文件
    with open("unprocessed_users.txt", "w", encoding="utf-8") as f:
        for uid in unprocessed:
            f.write(f"{uid}\n")
    
    print(f"\n未处理用户列表已保存到 unprocessed_users.txt")
