@echo off
echo ========================================
echo DEI数据处理环境设置脚本
echo ========================================

REM 激活MACIT虚拟环境
echo 正在激活MACIT虚拟环境...
call conda activate MACIT

REM 检查环境是否激活成功
if errorlevel 1 (
    echo 错误：无法激活MACIT虚拟环境
    echo 请先创建MACIT虚拟环境：
    echo conda create -n MACIT python=3.10
    pause
    exit /b 1
)

echo MACIT虚拟环境已激活

REM 安装必要的Python包
echo ========================================
echo 正在安装必要的Python包...
echo ========================================

echo 安装camel-ai...
pip install camel-ai

echo 安装openai...
pip install openai

echo 安装其他依赖...
pip install requests numpy pandas

REM 检查安装结果
echo ========================================
echo 验证安装结果...
echo ========================================

python -c "import camel; print('✅ camel-ai 安装成功')" 2>nul || echo "❌ camel-ai 安装失败"
python -c "import openai; print('✅ openai 安装成功')" 2>nul || echo "❌ openai 安装失败"

echo ========================================
echo 运行环境测试...
echo ========================================

python test_dei_setup.py

echo ========================================
echo 环境设置完成！
echo 
echo 下一步操作：
echo 1. 测试单个用户：python process_dei_data.py --user_id 75985435
echo 2. 批量处理：python run_all_dei_data.py --batch_size 10
echo 3. 查看详细说明：README_DEI_Processing.md
echo ========================================

pause
