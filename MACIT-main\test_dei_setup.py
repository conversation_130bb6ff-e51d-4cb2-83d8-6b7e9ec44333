#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试DEI数据处理设置
验证环境、数据路径、配置等是否正确
"""

import json
import logging
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from logger_config import setup_logger
from data_utils import load_user_profiles, load_topic_background

# 设置日志
logger = setup_logger('dei_setup_test')


def test_environment():
    """测试Python环境"""
    print("🔍 测试Python环境...")
    
    try:
        import camel
        print(f"✅ CAMEL库已安装: {camel.__version__ if hasattr(camel, '__version__') else '版本未知'}")
    except ImportError:
        print("❌ CAMEL库未安装")
        return False
    
    try:
        import openai
        print(f"✅ OpenAI库已安装: {openai.__version__ if hasattr(openai, '__version__') else '版本未知'}")
    except ImportError:
        print("❌ OpenAI库未安装")
        return False
    
    return True


def test_data_paths():
    """测试数据路径"""
    print("\n🔍 测试数据路径...")
    
    # 检查DEI数据目录
    dei_data_dir = Path("data/cut/DEI_intera_data/intera_data")
    if dei_data_dir.exists():
        json_files = list(dei_data_dir.glob("*.json"))
        print(f"✅ DEI数据目录存在: {dei_data_dir}")
        print(f"✅ 发现 {len(json_files)} 个DEI用户数据文件")
        
        # 测试读取一个文件
        if json_files:
            test_file = json_files[0]
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ 成功读取测试文件: {test_file.name}")
                print(f"   用户ID: {data.get('user_id', 'N/A')}")
                print(f"   交互数量: {len(data.get('interactions', []))}")
            except Exception as e:
                print(f"❌ 读取测试文件失败: {e}")
                return False
    else:
        print(f"❌ DEI数据目录不存在: {dei_data_dir}")
        return False
    
    # 检查用户画像文件
    user_profiles_path = Path("data/cut/processed_user_profiles/real_cut_user_profiles_500_fixed.json")
    if user_profiles_path.exists():
        print(f"✅ 用户画像文件存在: {user_profiles_path}")
    else:
        print(f"⚠️ 用户画像文件不存在: {user_profiles_path}")
    
    # 检查话题背景文件
    topic_bg_path = Path("data/cut/cut_description.txt")
    if topic_bg_path.exists():
        print(f"✅ 话题背景文件存在: {topic_bg_path}")
    else:
        print(f"⚠️ 话题背景文件不存在: {topic_bg_path}")
    
    return True


def test_output_directory():
    """测试输出目录"""
    print("\n🔍 测试输出目录...")
    
    # 创建DEI_output目录
    dei_output_dir = Path("DEI_output")
    dei_output_dir.mkdir(exist_ok=True)
    print(f"✅ DEI输出目录已创建: {dei_output_dir}")
    
    # 测试写入权限
    test_file = dei_output_dir / "test_write.txt"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试写入权限")
        test_file.unlink()  # 删除测试文件
        print("✅ 输出目录写入权限正常")
    except Exception as e:
        print(f"❌ 输出目录写入权限异常: {e}")
        return False
    
    return True


def test_configuration():
    """测试配置"""
    print("\n🔍 测试配置...")
    
    try:
        from macit_config import MODEL_CONFIG, TASK_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查API配置
        api_key = MODEL_CONFIG.get("api_key", "")
        if api_key and api_key.startswith("sk-"):
            print(f"✅ API密钥配置正确: {api_key[:10]}...")
        else:
            print("⚠️ API密钥配置可能有问题")
        
        print(f"✅ 模型名称: {MODEL_CONFIG.get('model_name', 'N/A')}")
        print(f"✅ 最大辩论轮数: {TASK_CONFIG.get('max_debate_rounds', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False
    
    return True


def test_data_loading():
    """测试数据加载功能"""
    print("\n🔍 测试数据加载功能...")
    
    try:
        # 测试加载用户画像
        user_profiles = load_user_profiles()
        print(f"✅ 成功加载 {len(user_profiles)} 个用户画像")
    except Exception as e:
        print(f"⚠️ 加载用户画像失败: {e}")
    
    try:
        # 测试加载话题背景
        topic_background = load_topic_background()
        print(f"✅ 成功加载话题背景，长度: {len(topic_background)} 字符")
    except Exception as e:
        print(f"⚠️ 加载话题背景失败: {e}")
    
    try:
        # 测试加载DEI交互数据
        from data_utils import load_interaction_data
        
        # 获取第一个DEI用户ID
        dei_data_dir = Path("data/cut/DEI_intera_data/intera_data")
        json_files = list(dei_data_dir.glob("*.json"))
        if json_files:
            test_user_id = json_files[0].stem
            interaction_data = load_interaction_data(test_user_id, use_dei_data=True)
            print(f"✅ 成功加载DEI用户 {test_user_id} 的交互数据")
            print(f"   交互数量: {len(interaction_data.get('interactions', []))}")
        else:
            print("⚠️ 没有DEI用户数据可供测试")
    except Exception as e:
        print(f"❌ 加载DEI交互数据失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始DEI数据处理设置测试")
    print("="*60)
    
    all_tests_passed = True
    
    # 运行各项测试
    tests = [
        ("Python环境", test_environment),
        ("数据路径", test_data_paths),
        ("输出目录", test_output_directory),
        ("配置文件", test_configuration),
        ("数据加载", test_data_loading),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if not result:
                all_tests_passed = False
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            all_tests_passed = False
    
    print("\n" + "="*60)
    if all_tests_passed:
        print("🎉 所有测试通过！DEI数据处理环境配置正确")
        print("\n📋 下一步操作:")
        print("1. 运行单个用户测试: python process_dei_data.py --user_id 75985435")
        print("2. 运行批量处理: python run_all_dei_data.py --batch_size 10")
        print("3. 使用批处理脚本: run_dei_processing.bat")
    else:
        print("❌ 部分测试失败，请检查配置和环境")
    print("="*60)


if __name__ == "__main__":
    main()
