# DEI数据处理指南

本指南说明如何使用MACIT框架处理DEI_intera_data目录中的数据，并将结果输出到DEI_output文件夹。

## 环境准备

### 1. 激活MACIT虚拟环境
```bash
conda activate MACIT
```

### 2. 安装必要的依赖包
```bash
pip install camel-ai openai
```

## 数据处理方式

### 方式一：使用Python脚本（推荐）

#### 1. 测试单个用户
```bash
python process_dei_data.py --user_id 75985435
```

#### 2. 处理前10个用户
```bash
python process_dei_data.py --max_users 10
```

#### 3. 批量处理所有用户
```bash
python run_all_dei_data.py --batch_size 20 --delay 3
```

#### 4. 断点续传处理
```bash
python run_all_dei_data.py --start_from 50 --skip_existing
```

### 方式二：使用批处理脚本
```bash
run_dei_processing.bat
```

## 脚本参数说明

### process_dei_data.py 参数
- `--user_id`: 指定要处理的用户ID
- `--max_users`: 最大处理用户数量
- `--max_rounds`: 最大辩论轮数（默认3轮）
- `--start_from`: 从第几个用户开始处理
- `--interaction_index`: 交互索引（默认0）

### run_all_dei_data.py 参数
- `--batch_size`: 每批处理的用户数量（默认50）
- `--start_from`: 从第几个用户开始处理
- `--max_users`: 最大处理用户数量
- `--delay`: 每个用户处理间隔（秒，默认3）
- `--skip_existing`: 跳过已处理的用户

## 输出结果

处理结果将保存在 `DEI_output` 文件夹中，结构如下：
```
DEI_output/
├── 75985435/
│   └── interaction_0.json
├── 1002788786/
│   └── interaction_0.json
└── ...
```

每个用户的结果文件包含：
- 上下文分析报告
- 辩论过程记录
- 候选标签
- 高级评审结果
- 最终结构化意图标签

## 数据统计

DEI数据集包含约500个用户的交互数据，每个用户包含多条社交媒体交互记录。

## 故障排除

### 1. 模块未找到错误
```
ModuleNotFoundError: No module named 'camel'
```
**解决方案**: 确保已激活MACIT环境并安装了camel-ai包

### 2. API密钥错误
```
Authentication failed
```
**解决方案**: 检查macit_config.py中的API密钥配置

### 3. 内容风险检查
某些用户可能触发内容风险检查而被跳过，这是正常现象。

### 4. 处理超时
如果单个用户处理时间过长，可以调整max_rounds参数减少辩论轮数。

## 监控处理进度

### 查看日志
```bash
tail -f logs/macit_*.log
```

### 查看进度文件
批量处理时会生成 `dei_progress.json` 文件记录处理进度。

### 检查输出目录
```bash
ls -la DEI_output/
```

## 性能优化建议

1. **批量大小**: 建议设置batch_size为20-50
2. **处理延迟**: 建议设置delay为2-5秒，避免API限流
3. **断点续传**: 使用--skip_existing参数避免重复处理
4. **并行处理**: 可以同时运行多个脚本处理不同的用户范围

## 示例命令

### 快速测试（处理5个用户）
```bash
python process_dei_data.py --max_users 5 --max_rounds 2
```

### 生产环境批量处理
```bash
python run_all_dei_data.py --batch_size 30 --delay 3 --skip_existing
```

### 处理特定用户范围
```bash
python run_all_dei_data.py --start_from 100 --max_users 50
```

## 预期处理时间

- 单个用户: 约30-60秒
- 100个用户: 约1-2小时
- 全部用户(~500): 约5-10小时

处理时间取决于网络状况、API响应速度和辩论轮数。
