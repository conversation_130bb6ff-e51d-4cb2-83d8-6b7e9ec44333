#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
MACIT (Multi-Agent Collaborative Intent Tagging) 框架核心模块
实现多智能体协作的意图标注功能
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional

from camel.agents import ChatAgent
from camel.messages import BaseMessage

from macit_config import PROMPT_TEMPLATES, TASK_CONFIG, OUTPUT_DIR
from data_utils import (
    load_user_profiles,
    load_topic_background,
    load_interaction_data,
    get_interaction_sample,
    format_user_profile,
    find_user_profile_by_id,
)
from model_interface import (
    create_agent, parse_response, calculate_similarity,
    evaluate_label_quality
)

# 配置日志
logger = logging.getLogger(__name__)


class MACITFramework:
    """
    MACIT (Multi-Agent Collaborative Intent Tagging) 框架
    实现多智能体协作的意图标注功能
    """

    def __init__(self):
        """初始化MACIT框架"""
        # 加载用户画像和话题背景
        self.user_profiles = load_user_profiles()
        self.topic_background = load_topic_background()

        # 创建智能体
        # 用户上下文理解智能体
        self.context_agent = create_agent(
            system_message=PROMPT_TEMPLATES["context_agent_system"],
            agent_id="context_analyst",
        )

        # 两个独立的意图标注智能体
        self.agent1 = create_agent(
            system_message=PROMPT_TEMPLATES["agent1_system"],
            agent_id="intent_analyst_1",
        )
        self.agent2 = create_agent(
            system_message=PROMPT_TEMPLATES["agent2_system"],
            agent_id="intent_analyst_2",
        )

        # 高级评审智能体
        self.senior_agent = create_agent(
            system_message=PROMPT_TEMPLATES["senior_agent_system"],
            agent_id="senior_evaluator",
        )

        # 初始化辩论历史和上下文报告
        self.debate_history = []
        self.context_report = {}

        logger.info("MACIT框架初始化完成，包含4个智能体")

    def _reset_agents_context(self):
        """
        重置所有智能体的上下文，防止历史对话累积
        """
        try:
            self.context_agent.reset()
            self.intent_agent_1.reset()
            self.intent_agent_2.reset()
            self.senior_agent.reset()
            logger.info("成功重置所有智能体的上下文")
        except Exception as e:
            logger.warning(f"重置智能体上下文时出现警告: {e}")

    def _is_content_safe(self, text: str) -> bool:
        """
        检查内容是否安全，避免触发内容风险检查

        Args:
            text: 要检查的文本内容

        Returns:
            bool: True表示内容安全，False表示可能有风险
        """
        # 为了测试目的，始终返回True
        logger.info("内容安全检查已禁用，忽略安全检查")
        return True

    def _truncate_text(self, text: str, max_length: int = 5000) -> str:
        """
        截断文本到指定长度

        Args:
            text: 原始文本
            max_length: 最大长度

        Returns:
            截断后的文本
        """
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."

    def _count_user_behaviors(self, interaction_data: Dict) -> Dict[str, int]:
        """
        统计用户的行为数量

        Args:
            interaction_data: 用户交互数据

        Returns:
            行为类型及其数量的字典
        """
        behavior_counts = {}
        interactions = interaction_data.get("interactions", [])

        for interaction in interactions:
            behavior_type = interaction.get("type", "unknown")
            behavior_counts[behavior_type] = behavior_counts.get(behavior_type, 0) + 1

        return behavior_counts

    def _generate_context_analysis(
        self,
        user_profile: Dict,
        interaction_sample: Dict,
    ) -> Dict:
        """
        生成上下文分析报告

        Args:
            user_profile: 用户画像
            interaction_sample: 交互样本

        Returns:
            上下文分析报告
        """
        logger.info("开始生成上下文分析报告")

        # 获取交互上下文和行为
        context = interaction_sample.get("context", {})
        context_text = context.get("text", "")
        context_author = context.get("author_username", "") or context.get("author_id", "")

        action_type = interaction_sample.get("type", "")
        action_text = interaction_sample.get("text", "")
        timestamp = interaction_sample.get("timestamp", "")

        # 格式化用户画像
        formatted_profile = format_user_profile(user_profile)

        # 截断长文本，防止超过模型限制
        context_text = self._truncate_text(context_text, 2000)
        action_text = self._truncate_text(action_text, 2000)
        topic_background = self._truncate_text(self.topic_background, 3000)

        # 使用上下文分析模板
        prompt = PROMPT_TEMPLATES["context_analysis_template"].format(
            topic_background=topic_background,
            user_profile=formatted_profile,
            context_text=context_text,
            context_author=context_author,
            action_type=action_type,
            action_text=action_text,
            timestamp=timestamp,
        )

        try:
            # 调用上下文分析智能体
            response = self.context_agent.step(prompt)
            thought, result = parse_response(response.msgs[0].content)

            logger.info("上下文分析报告生成完成")

            return {
                "thought": thought,
                "analysis": result
            }

        except Exception as e:
            error_msg = str(e)
            if "Content Exists Risk" in error_msg or "Content Risk" in error_msg:
                logger.error("上下文分析触发内容风险检查")
                raise ValueError("上下文分析内容风险")
            else:
                logger.error(f"上下文分析失败: {e}")
                raise

    def _prepare_intent_labeling_prompt(
        self,
        user_profile: Dict,
        interaction_sample: Dict,
        context_report: Dict,
        round_num: int,
        previous_discussion: str,
        other_opinion: str,
        behavior_counts: Dict[str, int] = None,
    ) -> str:
        """
        准备意图标注提示

        Args:
            user_profile: 用户画像
            interaction_sample: 交互样本
            context_report: 上下文分析报告
            round_num: 当前轮次
            previous_discussion: 之前的讨论内容
            other_opinion: 对方的意见

        Returns:
            意图标注提示
        """
        # 获取交互上下文和行为
        context = interaction_sample.get("context", {})
        context_text = context.get("text", "")
        context_author = context.get("author_username", "") or context.get("author_id", "")

        action_type = interaction_sample.get("type", "")
        action_text = interaction_sample.get("text", "")

        # 获取该行为类型的数量
        behavior_quantity = 1  # 默认值
        if behavior_counts and action_type in behavior_counts:
            behavior_quantity = behavior_counts[action_type]

        # 格式化用户画像
        formatted_profile = format_user_profile(user_profile)

        # 格式化上下文报告
        context_report_str = json.dumps(context_report.get("analysis", {}),
                                       ensure_ascii=False, indent=2)

        # 使用意图标注模板生成提示
        prompt = PROMPT_TEMPLATES["intent_labeling_template"].format(
            topic_background=self.topic_background,
            user_profile=formatted_profile,
            context_report=context_report_str,
            context_text=context_text,
            context_author=context_author,
            action_type=action_type,
            action_text=action_text,
            behavior_quantity=behavior_quantity,
            round_num=round_num,
            previous_discussion=previous_discussion,
            other_opinion=other_opinion,
        )

        return prompt

    def _conduct_debate_round(
        self,
        user_profile: Dict,
        interaction_sample: Dict,
        context_report: Dict,
        round_num: int,
        previous_discussion: str = "",
        behavior_counts: Dict[str, int] = None,
    ) -> Tuple[Dict, Dict, bool]:
        """
        进行一轮辩论

        Args:
            user_profile: 用户画像
            interaction_sample: 交互样本
            context_report: 上下文分析报告
            round_num: 当前轮次
            previous_discussion: 之前的讨论内容

        Returns:
            智能体1的分析结果、智能体2的分析结果、是否达成共识
        """
        logger.info(f"开始第 {round_num} 轮辩论")

        # 第一轮辩论，两个智能体独立分析
        if round_num == 1:
            # 智能体1分析
            prompt1 = self._prepare_intent_labeling_prompt(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=context_report,
                round_num=round_num,
                previous_discussion="",
                other_opinion="",
                behavior_counts=behavior_counts,
            )
            response1 = self.agent1.step(prompt1)
            thought1, result1 = parse_response(response1.msgs[0].content)

            # 智能体2分析
            prompt2 = self._prepare_intent_labeling_prompt(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=context_report,
                round_num=round_num,
                previous_discussion="",
                other_opinion="",
                behavior_counts=behavior_counts,
            )
            response2 = self.agent2.step(prompt2)
            thought2, result2 = parse_response(response2.msgs[0].content)

            # 记录辩论历史
            self.debate_history.append({
                "round": round_num,
                "agent1": {
                    "thought": thought1,
                    "result": result1,
                },
                "agent2": {
                    "thought": thought2,
                    "result": result2,
                },
            })

            # 检查是否达成共识 - 使用新的结构化标签
            label1 = result1.get("structured_intent_label", {})
            label2 = result2.get("structured_intent_label", {})

            similarity = calculate_similarity(label1, label2)

            # 使用新的阈值配置
            high_threshold = TASK_CONFIG["high_similarity_threshold"]
            medium_threshold = TASK_CONFIG["medium_similarity_threshold"]

            if similarity >= high_threshold:
                consensus = True
                logger.info(f"第 {round_num} 轮辩论完成，相似度: {similarity:.2f}, 达成高度共识")
            elif similarity >= medium_threshold:
                consensus = False  # 需要进一步辩论
                logger.info(f"第 {round_num} 轮辩论完成，相似度: {similarity:.2f}, 存在中等分歧")
            else:
                consensus = False  # 需要辩论
                logger.info(f"第 {round_num} 轮辩论完成，相似度: {similarity:.2f}, 存在显著分歧")

            return result1, result2, consensus

        # 后续轮次，两个智能体相互评价对方的分析
        else:
            # 获取上一轮的分析结果
            last_round = self.debate_history[-1]
            agent1_last_result = last_round["agent1"]["result"]
            agent2_last_result = last_round["agent2"]["result"]

            # 智能体1评价智能体2的分析
            prompt1 = self._prepare_intent_labeling_prompt(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=context_report,
                round_num=round_num,
                previous_discussion=previous_discussion,
                other_opinion=json.dumps(agent2_last_result, ensure_ascii=False, indent=2),
                behavior_counts=behavior_counts,
            )
            response1 = self.agent1.step(prompt1)
            thought1, result1 = parse_response(response1.msgs[0].content)

            # 智能体2评价智能体1的分析
            prompt2 = self._prepare_intent_labeling_prompt(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=context_report,
                round_num=round_num,
                previous_discussion=previous_discussion,
                other_opinion=json.dumps(agent1_last_result, ensure_ascii=False, indent=2),
                behavior_counts=behavior_counts,
            )
            response2 = self.agent2.step(prompt2)
            thought2, result2 = parse_response(response2.msgs[0].content)

            # 记录辩论历史
            self.debate_history.append({
                "round": round_num,
                "agent1": {
                    "thought": thought1,
                    "result": result1,
                },
                "agent2": {
                    "thought": thought2,
                    "result": result2,
                },
            })

            # 检查是否达成共识
            agent1_accepts = result1.get("accept_other_opinion", False)
            agent2_accepts = result2.get("accept_other_opinion", False)

            # 如果有一方接受另一方的意见，则认为达成共识
            consensus = agent1_accepts or agent2_accepts

            logger.info(f"第 {round_num} 轮辩论完成，智能体1接受: {agent1_accepts}, 智能体2接受: {agent2_accepts}, 是否达成共识: {consensus}")

            return result1, result2, consensus

    def _generate_previous_discussion(self) -> str:
        """
        生成之前的讨论内容

        Returns:
            之前的讨论内容
        """
        if not self.debate_history:
            return ""

        discussion = []

        for round_data in self.debate_history:
            round_num = round_data["round"]
            agent1_result = round_data["agent1"]["result"]
            agent2_result = round_data["agent2"]["result"]

            discussion.append(f"## 第 {round_num} 轮辩论")

            # 添加智能体1的分析
            discussion.append("### 智能体1分析")
            discussion.append(f"意图分析: {json.dumps(agent1_result.get('intent_analysis', {}), ensure_ascii=False)}")

            # 添加智能体2的分析
            discussion.append("### 智能体2分析")
            discussion.append(f"意图分析: {json.dumps(agent2_result.get('intent_analysis', {}), ensure_ascii=False)}")

            # 如果不是第一轮，添加评价和接受情况
            if round_num > 1:
                discussion.append("### 智能体1对智能体2的评价")
                discussion.append(agent1_result.get("evaluation_of_other_opinion", ""))
                discussion.append(f"是否接受: {agent1_result.get('accept_other_opinion', False)}")

                discussion.append("### 智能体2对智能体1的评价")
                discussion.append(agent2_result.get("evaluation_of_other_opinion", ""))
                discussion.append(f"是否接受: {agent2_result.get('accept_other_opinion', False)}")

        return "\n\n".join(discussion)

    def _conduct_senior_evaluation(
        self,
        user_profile: Dict,
        interaction_sample: Dict,
        context_report: Dict,
        candidate_labels: List[Dict],
    ) -> Dict:
        """
        进行高级评审

        Args:
            user_profile: 用户画像
            interaction_sample: 交互样本
            context_report: 上下文分析报告
            candidate_labels: 候选标签列表

        Returns:
            评审结果
        """
        logger.info("开始高级评审")

        # 获取交互上下文和行为
        context = interaction_sample.get("context", {})
        context_text = context.get("text", "")
        context_author = context.get("author_username", "") or context.get("author_id", "")

        action_type = interaction_sample.get("type", "")
        action_text = interaction_sample.get("text", "")

        # 格式化用户画像
        formatted_profile = format_user_profile(user_profile)

        # 格式化上下文报告
        context_report_str = json.dumps(context_report.get("analysis", {}),
                                       ensure_ascii=False, indent=2)

        # 格式化候选标签
        candidate_labels_str = json.dumps(candidate_labels, ensure_ascii=False, indent=2)

        # 格式化辩论记录
        debate_history_str = json.dumps(self.debate_history, ensure_ascii=False, indent=2)

        # 使用高级评审模板
        prompt = PROMPT_TEMPLATES["senior_evaluation_template"].format(
            topic_background=self.topic_background,
            user_profile=formatted_profile,
            context_report=context_report_str,
            context_text=context_text,
            context_author=context_author,
            action_type=action_type,
            action_text=action_text,
            candidate_labels=candidate_labels_str,
            debate_history=debate_history_str,
        )

        # 调用高级评审智能体
        response = self.senior_agent.step(prompt)
        thought, result = parse_response(response.msgs[0].content)

        logger.info("高级评审完成")

        return {
            "thought": thought,
            "evaluation": result
        }

    def _determine_final_intent(self) -> Dict:
        """
        确定最终意图

        Returns:
            最终意图
        """
        if not self.debate_history:
            return {}

        # 获取最后一轮辩论的结果
        last_round = self.debate_history[-1]
        agent1_result = last_round["agent1"]["result"]
        agent2_result = last_round["agent2"]["result"]

        # 检查是否有一方接受另一方的意见
        agent1_accepts = agent1_result.get("accept_other_opinion", False)
        agent2_accepts = agent2_result.get("accept_other_opinion", False)

        # 如果智能体1接受智能体2的意见
        if agent1_accepts:
            return agent1_result.get("final_intent_label", {})

        # 如果智能体2接受智能体1的意见
        if agent2_accepts:
            return agent2_result.get("final_intent_label", {})

        # 如果双方都不接受对方的意见，则返回智能体1的意图
        # 这里可以实现更复杂的决策逻辑，如引入第三方智能体进行裁决
        return agent1_result.get("final_intent_label", {})

    def analyze_user_intent(
        self,
        user_id: str,
        interaction_index: int = 0,
        max_rounds: int = None,
        use_dei_data: bool = False,
    ) -> Dict:
        """
        分析用户意图（新的细粒度结构化意图标签模型）

        Args:
            user_id: 用户ID
            interaction_index: 交互索引
            max_rounds: 最大辩论轮数
            use_dei_data: 是否使用DEI数据路径

        Returns:
            分析结果
        """
        if max_rounds is None:
            max_rounds = TASK_CONFIG["max_debate_rounds"]

        # 重置智能体上下文，防止历史对话累积
        self._reset_agents_context()

        # 重置辩论历史和上下文报告
        self.debate_history = []
        self.context_report = {}

        # 加载用户交互数据
        interaction_data = load_interaction_data(user_id, use_dei_data=use_dei_data)
        interaction_sample = get_interaction_sample(interaction_data, interaction_index)

        # 内容安全检查
        user_text = interaction_sample.get("text", "")
        context_text = interaction_sample.get("context_text", "")

        if not self._is_content_safe(user_text):
            logger.warning(f"用户 {user_id} 的内容可能存在风险，跳过分析")
            raise ValueError(f"用户内容安全检查失败: {user_id}")

        if not self._is_content_safe(context_text):
            logger.warning(f"用户 {user_id} 的上下文内容可能存在风险，跳过分析")
            raise ValueError(f"上下文内容安全检查失败: {user_id}")

        # 截断过长的文本
        interaction_sample["text"] = self._truncate_text(user_text, 3000)
        interaction_sample["context_text"] = self._truncate_text(context_text, 3000)

        # 统计用户的行为数量
        behavior_counts = self._count_user_behaviors(interaction_data)

        # 查找用户画像
        user_profile = find_user_profile_by_id(user_id, self.user_profiles)

        if not user_profile:
            logger.warning(f"未找到用户 {user_id} 的画像，使用空画像")
            user_profile = {}

        # 第一步：生成上下文分析报告
        logger.info("第一步：生成上下文分析报告")
        self.context_report = self._generate_context_analysis(
            user_profile=user_profile,
            interaction_sample=interaction_sample,
        )

        # 第二步：两个独立的意图标注智能体进行分析
        logger.info("第二步：独立意图标注")
        round_num = 1
        consensus = False

        while round_num <= max_rounds and not consensus:
            previous_discussion = self._generate_previous_discussion()
            _, _, consensus = self._conduct_debate_round(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=self.context_report,
                round_num=round_num,
                previous_discussion=previous_discussion,
                behavior_counts=behavior_counts,
            )

            # 下一轮
            round_num += 1

            # 添加延迟，避免过快请求
            time.sleep(1)

        # 第三步：收集候选标签
        candidate_labels = []
        if self.debate_history:
            last_round = self.debate_history[-1]

            # 添加智能体1的标签
            agent1_label = last_round["agent1"]["result"].get("structured_intent_label", {})
            if agent1_label:
                candidate_labels.append({
                    "label_id": "agent1_final",
                    "label": agent1_label,
                    "source": "agent1"
                })

            # 添加智能体2的标签
            agent2_label = last_round["agent2"]["result"].get("structured_intent_label", {})
            if agent2_label:
                candidate_labels.append({
                    "label_id": "agent2_final",
                    "label": agent2_label,
                    "source": "agent2"
                })

        # 第四步：高级评审
        logger.info("第四步：高级评审")
        senior_evaluation = None
        final_intent = {}

        if candidate_labels:
            senior_evaluation = self._conduct_senior_evaluation(
                user_profile=user_profile,
                interaction_sample=interaction_sample,
                context_report=self.context_report,
                candidate_labels=candidate_labels,
            )

            # 从评审结果中提取最终意图
            evaluation_result = senior_evaluation.get("evaluation", {})
            final_decision = evaluation_result.get("final_decision", {})
            selected_label_id = final_decision.get("selected_label_id", "")

            # 找到选中的标签
            for candidate in candidate_labels:
                if candidate["label_id"] == selected_label_id:
                    final_intent = candidate["label"]
                    break

            # 如果没有找到，使用第一个候选标签
            if not final_intent and candidate_labels:
                final_intent = candidate_labels[0]["label"]
        else:
            # 如果没有候选标签，使用传统方法
            final_intent = self._determine_final_intent()

        # 构建分析结果
        analysis_result = {
            "user_id": user_id,
            "interaction_index": interaction_index,
            "context_analysis": self.context_report,
            "debate_rounds": len(self.debate_history),
            "consensus_reached": consensus,
            "candidate_labels": candidate_labels,
            "senior_evaluation": senior_evaluation,
            "final_structured_intent_label": final_intent,
            "debate_history": self.debate_history,
        }

        logger.info(f"用户 {user_id} 的细粒度意图分析完成，共进行 {len(self.debate_history)} 轮辩论，是否达成共识: {consensus}")

        return analysis_result

    def save_analysis_result(self, result: Dict, user_id: str, interaction_index: int) -> str:
        """
        保存分析结果

        Args:
            result: 分析结果
            user_id: 用户ID
            interaction_index: 交互索引

        Returns:
            保存路径
        """
        # 创建输出目录
        output_path = OUTPUT_DIR / f"{user_id}"
        output_path.mkdir(exist_ok=True, parents=True)

        # 保存文件
        file_path = output_path / f"interaction_{interaction_index}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        logger.info(f"分析结果已保存至 {file_path}")

        return str(file_path)