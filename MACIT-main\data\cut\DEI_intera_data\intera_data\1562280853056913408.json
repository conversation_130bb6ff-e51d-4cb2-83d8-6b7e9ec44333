{"user_id": "1562280853056913408", "interactions_count": 39, "interactions": [{"conversation_id": "1922902155700158471", "tweet_id": "1923636303779750046", "timestamp": "2025-05-17T07:06:48+00:00", "timestamp_unix": 1747465608, "type": "comment", "text": "@RomanceFGC @Superaustin152 MK fans, general audience doesnt know", "context": {"type": "tweet", "id": "1924308022844690837", "text": "We all know what they mean by DEI, &amp; that's disgusting. But she is a character that borrows heavily from the iconography from another character she has loose ties too in comic, let alone the MCU.\n\nPlus I just don't remember in in WF, so this will be her introduction.", "author_id": "1303183980842414080", "author_username": "PassionSod"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923636824703894010", "timestamp": "2025-05-17T07:08:53+00:00", "timestamp_unix": 1747465733, "type": "comment", "text": "@Superaustin152 @RomanceFGC How come every mantle can be passed down. But when its time for a black guy to take over the biggest hero in comics, now you dont want to get rid of him?", "context": {"type": "tweet", "id": "1924308022844690837", "text": "We all know what they mean by DEI, &amp; that's disgusting. But she is a character that borrows heavily from the iconography from another character she has loose ties too in comic, let alone the MCU.\n\nPlus I just don't remember in in WF, so this will be her introduction.", "author_id": "1303183980842414080", "author_username": "PassionSod"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923637242435719526", "timestamp": "2025-05-17T07:10:32+00:00", "timestamp_unix": 1747465832, "type": "comment", "text": "@Superaustin152 @RomanceFGC Not time travel back. I mean is <PERSON> a permanent Batman in <PERSON><PERSON>’s time as <PERSON>? <PERSON> is A Batman. Not THE Batman. Same with <PERSON>.", "context": {"type": "tweet", "id": "1924308022844690837", "text": "We all know what they mean by DEI, &amp; that's disgusting. But she is a character that borrows heavily from the iconography from another character she has loose ties too in comic, let alone the MCU.\n\nPlus I just don't remember in in WF, so this will be her introduction.", "author_id": "1303183980842414080", "author_username": "PassionSod"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923718060143161712", "timestamp": "2025-05-17T12:31:41+00:00", "timestamp_unix": 1747485101, "type": "comment", "text": "@Superaustin152 @RomanceFGC I understand that. My main talking point is <PERSON> should just fully take over fully as the main Spider-Man. Especially being in the 616. <PERSON> is in Ultimate. <PERSON> has been received well by the public. I think its time for <PERSON> to go and <PERSON> to be the only Spider-Man.", "context": {"type": "tweet", "id": "1923712242698101018", "text": "@CShelby316 @RomanceFGC What are you talking about?\nI've never said I want a hero to be  fully replaced anywhere in this conversation. \nI've been talking about how I'm fine with heroes sharing names and now you're trying to make it sound like I have a issue because <PERSON> is <PERSON>?", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923732489341644945", "timestamp": "2025-05-17T13:29:01+00:00", "timestamp_unix": 1747488541, "type": "comment", "text": "@Superaustin152 @RomanceF<PERSON> <PERSON>, <PERSON>, <PERSON>, every other white mantle had the previous guy step down. But now its a problem when the successor is black?", "context": {"type": "tweet", "id": "1923729048754159917", "text": "@CShelby316 @RomanceFGC I don't think that's needed they can co-exist and have been for years, and it's great.\nPlus Ultimate is a Different Peter", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923759935705293107", "timestamp": "2025-05-17T15:18:05+00:00", "timestamp_unix": 1747495085, "type": "comment", "text": "@Superaustin152 @RomanceFGC Because every other hero lets the white guy gets to run solo. But when it comes to passing the mantle to a black guy people have a problem with it.", "context": {"type": "tweet", "id": "1923733432393159066", "text": "@CShelby316 @RomanceFGC Why do you keep coming back to that? never said it was a problem, and also, there were 4 flash at a times and lots of earth GLs all co existing what I'm saying is they can co-exist and still have mantles pass on.", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923760348286333173", "timestamp": "2025-05-17T15:19:43+00:00", "timestamp_unix": 1747495183, "type": "comment", "text": "@Superaustin152 @RomanceFGC What issue is there with letting go of <PERSON>? Even temporary. Captain <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Woman, <PERSON>, etc aloud their successors to run solo as the only one. But <PERSON> cant get that treatment and its stupid. He always has to share", "context": {"type": "tweet", "id": "1923733455352844678", "text": "@CShelby316 @RomanceFGC I've thought you've been actually a nice person unlike most others in these conversations, but now I'm starting to think you just want to start issues for no reason", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 33}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923832657219084528", "timestamp": "2025-05-17T20:07:03+00:00", "timestamp_unix": 1747512423, "type": "comment", "text": "@Superaustin152 @RomanceFGC Then they shoved him in 616 and hes the other Spider-Man now. They should have kept him on his own. Just let <PERSON> die and have <PERSON> take over for the next 60 years", "context": {"type": "tweet", "id": "1923779853532156329", "text": "@CShelby316 @RomanceFGC They did that with <PERSON> that's how he started before moving universes he had a good amount of time without a <PERSON> around it would Just repeating at this point.\nAnd <PERSON> has tons of adventures without <PERSON>", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923833093082767815", "timestamp": "2025-05-17T20:08:47+00:00", "timestamp_unix": 1747512527, "type": "comment", "text": "@Superaustin152 @RomanceFGC Green Lanterns are space police. Its not a mantle its a job. <PERSON> eventually came back. <PERSON> was gone for 20 years before he came back. None of the <PERSON>s share the name at the same time", "context": {"type": "tweet", "id": "1923779506558443975", "text": "@CShelby316 @RomanceFGC I don't, and only idiots have that issue, but also, they have co existed <PERSON> and <PERSON> have for years and <PERSON> and <PERSON> have too both as <PERSON> same with <PERSON> and <PERSON> it's fine .\nAnd they still have their solo adventures", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923834270042263763", "timestamp": "2025-05-17T20:13:27+00:00", "timestamp_unix": 1747512807, "type": "comment", "text": "@Superaustin152 @RomanceF<PERSON> <PERSON>, <PERSON>, <PERSON>, Captain <PERSON>, <PERSON>, etc. all had their replacements be the only one in main continuity. But <PERSON><PERSON><PERSON> is an exception for what reason?", "context": {"type": "tweet", "id": "1923833829334090046", "text": "@CShelby316 @RomanceFGC They are both the Spider-Man. \nAnd no way they'd ever get rid of <PERSON> for 60 years.\nThey can co exist it's not a issue\n\nHeck they've had 3 Spider-Men at once for several years", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923840076942872689", "timestamp": "2025-05-17T20:36:32+00:00", "timestamp_unix": 1747514192, "type": "comment", "text": "@Superaustin152 @RomanceFGC Arent Scarlet Spider fans upset hes underutilized? <PERSON><PERSON>, <PERSON>, <PERSON>, Etc are all underutilized rn", "context": {"type": "tweet", "id": "1923834708078506208", "text": "@CShelby316 @RomanceFGC <PERSON> has been replaced in 616 before. \n\nAnd all those other ones co exist now with the others just like <PERSON> and <PERSON> it's not a issue", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923840211835867248", "timestamp": "2025-05-17T20:37:04+00:00", "timestamp_unix": 1747514224, "type": "comment", "text": "@Superaustin152 @RomanceFGC Is there atill 4 Robins at the same time ir is <PERSON> the main one and the others showed up for a short run?", "context": {"type": "tweet", "id": "1923834429081874763", "text": "@CShelby316 @RomanceFGC Also there was a whole Robin group they had a whole comic", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923840499225395576", "timestamp": "2025-05-17T20:38:12+00:00", "timestamp_unix": 1747514292, "type": "comment", "text": "@Superaustin152 @RomanceFGC Because <PERSON> being Spider-Man iss always compared to Green Lantern. But people dont care to realize GL is a group.", "context": {"type": "tweet", "id": "1923834373767405590", "text": "@CShelby316 @RomanceFGC You used GL for a point to this you can't say it doesn't count now that I use it\nYes <PERSON> came back that's the Point him and <PERSON> are both Captain America together there's even others right now\nAnd now we have 4 Flashs\nYou're the one who used <PERSON> I didn't say anything about him", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923877513626804250", "timestamp": "2025-05-17T23:05:17+00:00", "timestamp_unix": 1747523117, "type": "comment", "text": "@Superaustin152 @RomanceFGC <PERSON> still got pushed back into a sideman role. <PERSON> hasn’t had his time as the only Spider-Man in 616. They can co-exist. But until <PERSON> moves on <PERSON> will be known more as <PERSON> than <PERSON><PERSON><PERSON>.", "context": {"type": "tweet", "id": "1923843628683678104", "text": "@CShelby316 @RomanceFGC <PERSON> took over for <PERSON> twice, and <PERSON><PERSON> did, too, and <PERSON> had his story about taking over. Of course, they don't want to re do that.\nThey can co-exist and have been for years\n But how <PERSON> is now some people are upset and I do wish <PERSON> was used more she's mostly in others comics", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923877582367293752", "timestamp": "2025-05-17T23:05:34+00:00", "timestamp_unix": 1747523134, "type": "comment", "text": "@Superaustin152 @RomanceFGC Im using points from other people", "context": {"type": "tweet", "id": "1923842970765193326", "text": "@CShelby316 @RomanceFGC You're the one who brought up The GL", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1922902155700158471", "tweet_id": "1923998707650834696", "timestamp": "2025-05-18T07:06:52+00:00", "timestamp_unix": 1747552012, "type": "comment", "text": "@Superaustin152 @RomanceFGC But theres no good reason to keep <PERSON>. Honestly he needs a break", "context": {"type": "tweet", "id": "1923907093091795215", "text": "@CShelby316 @RomanceFGC Not really both are known as Spider-<PERSON>. Only the people who hate <PERSON> won't call him Spider-Man, and those guys are mainly idiots who hate him for really bad reasons .\nThere's no reason he has to be the only Spider-Man at this point", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924218366404739180", "timestamp": "2025-05-18T21:39:43+00:00", "timestamp_unix": 1747604383, "type": "comment", "text": "@Superaustin152 @RomanceFGC <PERSON>-Man is the mascot. Not <PERSON>.", "context": {"type": "tweet", "id": "1924092669774422503", "text": "@CShelby316 @RomanceFGC He's their mascot. Of course, they keep him, and he still gets good stories .\nPlus, he has gotten breaks before", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924271272390774878", "timestamp": "2025-05-19T01:09:57+00:00", "timestamp_unix": 1747616997, "type": "comment", "text": "@Superaustin152 @RomanceFGC So <PERSON> is just forever the 2nd Spider-Man. Does he not deserve to take over forever and be the mascot for a more progressive age?", "context": {"type": "tweet", "id": "1924221098020778209", "text": "@CShelby316 @RomanceF<PERSON> <PERSON> is Marvel's Masco<PERSON>", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924294361992331441", "timestamp": "2025-05-19T02:41:42+00:00", "timestamp_unix": 1747622502, "type": "comment", "text": "@Superaustin152 @RomanceFGC Im not saying hes less, but as much as Marvel wants them to be equal. Even <PERSON> consider <PERSON> as the “real” Spider-Man. \n\nLook at Marvel Rivals. Perfect opportunity to add <PERSON> over <PERSON>. Hes Spider-Man at the end of the day. Where is the <PERSON> solo tv series? Solo movie?", "context": {"type": "tweet", "id": "1924287334398218557", "text": "@CShelby316 @RomanceFGC Feel like you're the one who's making <PERSON> seem like less while I'm the one saying he's not", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924464863054631304", "timestamp": "2025-05-19T13:59:12+00:00", "timestamp_unix": 1747663152, "type": "comment", "text": "@Superaustin152 @RomanceFGC But <PERSON> is still treated as the main one. He<PERSON> always chooses first over <PERSON>. \n\nAnd yeah Spider-Verse stared <PERSON> but it also had other Spider<PERSON> in it. \n\nLook at MilesPS4. Thats the first time he has been Spider-Man on his own outside of comics. We need more of that", "context": {"type": "tweet", "id": "1924439111391166853", "text": "@CShelby316 @RomanceFGC They treat them both as the real ones.\nAnd they can still add <PERSON>.\n<PERSON><PERSON>, \nwe don't even know who or how they picked the starting cast.\n\nSpider-Verse is <PERSON>'s movie it's about him.\n\nAnd this stuff takes time they'd probably want <PERSON>'s actor to be older before <PERSON>", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924507712026517938", "timestamp": "2025-05-19T16:49:28+00:00", "timestamp_unix": 1747673368, "type": "comment", "text": "@Superaustin152 @RomanceFGC I said outside of comics. In comics they still have <PERSON> around. He<PERSON> still the main Spider-<PERSON>. <PERSON> is good as a supporting role until <PERSON> moves on.", "context": {"type": "tweet", "id": "1924483792883855690", "text": "@CShelby316 @RomanceFGC That's literally not true\nThe 2nd Spider-verse event <PERSON> was in tie-ins <PERSON> was the lead, then got a Spider-Verse Mini series to himself he even got crossover events with no <PERSON>.\n<PERSON> was major in Civil War 2 <PERSON> wasn't. \n\nBut it was <PERSON>'s story", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924513096246186036", "timestamp": "2025-05-19T17:10:52+00:00", "timestamp_unix": 1747674652, "type": "comment", "text": "@Superaustin152 @RomanceFGC Miles isnt the main one. Its hard to see them equally as Spider-Man (which they are) when we really refer to <PERSON> when it comes to the topic if Spider-<PERSON>. \n\n<PERSON> came come across like a gimmick. Hes more of a “Also Spider-Man” rather than THE Spider-Man", "context": {"type": "tweet", "id": "1924511483645268260", "text": "@CShelby316 @RomanceFGC That's not what your first sentence was about.\nBoth are Spider-Man, and both are important. \n<PERSON> is only supporting when it's someone else's story, same with <PERSON> when he's in <PERSON> story.", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924518532768780673", "timestamp": "2025-05-19T17:32:28+00:00", "timestamp_unix": 1747675948, "type": "comment", "text": "@Superaustin152 @RomanceFGC <PERSON> hasnt been the main <PERSON> since forever. Can <PERSON> not retire and let <PERSON> take over? Having <PERSON> come in and also be <PERSON><PERSON><PERSON> is a gimmick. Hes the Spider-Man from the ultimate universe. https://t.co/uSqJcYS0NF", "context": {"type": "tweet", "id": "1924513513386443008", "text": "@CShelby316 @RomanceFGC Both are The Spider-Man and are treated just like that if they both get their own big storylines and are major to the Marvel universe. \n\nHe's no more of a gimmick then <PERSON> or any other superhero", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924525519380361316", "timestamp": "2025-05-19T18:00:14+00:00", "timestamp_unix": 1747677614, "type": "comment", "text": "@Superaustin152 @RomanceFGC Jay isnt the main flash and hasn’t been considered the main flash since forever. Even <PERSON> retired. Are comics not allowed to move on from characters? What more does <PERSON> have that cant be better told with <PERSON>?", "context": {"type": "tweet", "id": "1924522622663966936", "text": "@CShelby316 @RomanceF<PERSON> <PERSON> is still the <PERSON>, the same as others he's part of teams and works with the other <PERSON>'s.\n <PERSON> and <PERSON> are both important and coexisting and even sharing a book \nAnd again, they've done the stroy of <PERSON> not being Spider-Man anymore several times even with....", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924525720576913608", "timestamp": "2025-05-19T18:01:02+00:00", "timestamp_unix": 1747677662, "type": "comment", "text": "@Superaustin152 @RomanceFGC Isn’t that Spider-Man cover from the ultimate universe?", "context": {"type": "tweet", "id": "1924522672051904962", "text": "@CShelby316 @RomanceFGC Miles him taking over was his original story\nNow they co exist, and it's great they both the Spider-Man they are rhe Spectacular Spider-Men https://t.co/U806rLnuBM", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924538375173726364", "timestamp": "2025-05-19T18:51:19+00:00", "timestamp_unix": 1747680679, "type": "comment", "text": "@Superaustin152 @RomanceF<PERSON> isnt the OG Flash. <PERSON> is. And hes barely used. Thats the role <PERSON> should be in. <PERSON> would he <PERSON> in this instant since hes new.", "context": {"type": "tweet", "id": "1924532106077614489", "text": "@CShelby316 @RomanceFGC And now they all co exist <PERSON> and <PERSON> share the main Flash Book\n\n<PERSON> and <PERSON> are both Spider-Man but different people they have different stories the current Amazing book couldn't be told with <PERSON>.", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924538853307633697", "timestamp": "2025-05-19T18:53:13+00:00", "timestamp_unix": 1747680793, "type": "comment", "text": "@Superaustin152 @RomanceFGC I was unaware thats why i asked. But whats the ratio of his books having <PERSON> vs <PERSON> Spider-Man in 616? Ive seen more books having to list his name than regular ones", "context": {"type": "tweet", "id": "1924527651751928243", "text": "@CShelby316 @RomanceFGC That's literally his first 616 comic", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924546844220166656", "timestamp": "2025-05-19T19:24:58+00:00", "timestamp_unix": 1747682698, "type": "comment", "text": "@Superaustin152 @RomanceFGC <PERSON> still the OG. Same as <PERSON> also isnt the mainFlash. He gets run every now and then. But when you think of <PERSON>, when <PERSON> needs a <PERSON>, they default to 1. <PERSON> left the <PERSON> role. And when he is <PERSON> theres always one", "context": {"type": "tweet", "id": "1924540915999215757", "text": "@CShelby316 @RomanceFGC To most people, <PERSON> is the <PERSON> of Flashs, and the point is they all still co-exist. <PERSON> just had a series, too and <PERSON> and <PERSON> share a book. \n<PERSON> is used more then you seem to think https://t.co/5M9jRJeq4o", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 114}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924547636880629843", "timestamp": "2025-05-19T19:28:07+00:00", "timestamp_unix": 1747682887, "type": "comment", "text": "@Superaustin152 @RomanceFGC For those comics you used, were there other Iron Men and Captain Americas? And the ratio to them having their names in the title is still far less than ones without it. <PERSON> has his name in his titles way more often.", "context": {"type": "tweet", "id": "1924540018602713394", "text": "@CShelby316 @RomanceFGC After this, they do the <PERSON> in the name, but he also is part of Spectacular Spider-Men \n\n<PERSON> and other heroes also had their names in titles too https://t.co/FlTsafx5K3", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924553613365448971", "timestamp": "2025-05-19T19:51:52+00:00", "timestamp_unix": 1747684312, "type": "comment", "text": "@Superaustin152 @RomanceFGC Are those series apart of 616 with <PERSON> still alive. And if they are truly equal then <PERSON>’s current run should be <PERSON>. \n\nThe ratio of <PERSON> having his name in the title still far out weighs without (all within 616) https://t.co/4lsOK9olhN", "context": {"type": "tweet", "id": "1924552646746132689", "text": "@CShelby316 @RomanceFGC There was <PERSON> as <PERSON> yes but but no other Iron Men.\n\nHave you actually counted? Because he's had 2 full series with his name not in the title more if you count Spider-Men", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924554699560157610", "timestamp": "2025-05-19T19:56:11+00:00", "timestamp_unix": 1747684571, "type": "comment", "text": "@Superaustin152 @RomanceFGC <PERSON> is still the 1st <PERSON>, same at <PERSON>. No matter how much fans say he isnt. <PERSON> is the first Spider-Man. But one hero doesn’t want the black man to have his spot. \n\n<PERSON> would be <PERSON> in this case, who was allowed to fully take over the mantle.", "context": {"type": "tweet", "id": "1924553325363552526", "text": "@CShelby316 @RomanceFGC <PERSON> a <PERSON> have literally both been super active Flashs at the same time for years. \n\nAnd <PERSON> is still active, and his history real-world rise is very different topic wise then <PERSON> and <PERSON>.\n\nThere actually was a team of <PERSON>'s", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924588701801197958", "timestamp": "2025-05-19T22:11:18+00:00", "timestamp_unix": 1747692678, "type": "comment", "text": "@Superaustin152 @RomanceFGC Im saying <PERSON> is afraid of letting <PERSON> be the only Spider-Man in 616 lol. And yet  <PERSON> has stepped aside to let <PERSON> be the main Flash. Same with <PERSON>, Captain <PERSON>, etc. What are the negatives of letting <PERSON> take over forever?", "context": {"type": "tweet", "id": "1924559041587552418", "text": "@CShelby316 @RomanceFGC So now you're saying <PERSON> doesn't want <PERSON> to be Spider-<PERSON>? He's literally okay with it. \n<PERSON> even asked \nAnd now they all fully co exist <PERSON> and <PERSON> was from different universes originally he didn't take over anything originally now they share a world and are both Flash", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 93}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924589257059922156", "timestamp": "2025-05-19T22:13:30+00:00", "timestamp_unix": 1747692810, "type": "comment", "text": "@Superaustin152 @RomanceFGC It does kinda matter. It paints <PERSON> as a flavor of Spider-Man. If he was truly respected equally, there would be no need to have majority of his solo run title it Miles before Spider man for a reason. Mini series. Not a main run", "context": {"type": "tweet", "id": "1924558437737775507", "text": "@CShelby316 @RomanceFGC They are equal the title of all the books say Spider-Man it doesn't matter if his real name is in it.\nAgain <PERSON> had like over 100 issues of books with <PERSON> in the title. \n\nThey share a Spider-Men book together, and <PERSON> has been the stars of different mini series", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924599226190721058", "timestamp": "2025-05-19T22:53:07+00:00", "timestamp_unix": 1747695187, "type": "comment", "text": "@Superaustin152 @RomanceFGC How media outside of comics never showcases him alone? He’s always surrounded by other Spider-Men. And <PERSON> always being relegated to Miles 1st Spider 2nd doesn’t paint him as equal when <PERSON> doesn’t need to remind you which one it is", "context": {"type": "tweet", "id": "1924590062156624070", "text": "@CShelby316 @RomanceFGC It's just a title you're taking it to seriously the only people who believe that are the idiots online who will be mad no matter what they do.\n\nIt's like half and half really with the titles .\n<PERSON> is great as is same with <PERSON> they co exist and even work together it's fine", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924599429400592437", "timestamp": "2025-05-19T22:53:55+00:00", "timestamp_unix": 1747695235, "type": "comment", "text": "@Superaustin152 @RomanceFGC When was the last time <PERSON> was used as the main Flash outside of comics?", "context": {"type": "tweet", "id": "1924589553962115122", "text": "@CShelby316 @RomanceFGC There's no reason he has to he the only one. \nAnd <PERSON> didn't step aside him and <PERSON> were from different universes, and once fused they co existed .\nAnd <PERSON> died.\n\nThere's no reason he has to them co-existing works great they are fun together and have good stories", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924615522424918383", "timestamp": "2025-05-19T23:57:52+00:00", "timestamp_unix": 1747699072, "type": "comment", "text": "@Superaustin152 @RomanceFGC The cartoon where he teams up with ever DC hero?", "context": {"type": "tweet", "id": "1924605476647428430", "text": "@CShelby316 @RomanceFGC That doesn't matter to this topic.\nAnd if you really think it does, he was the most recurring Flash in <PERSON> the Brave and the Bold", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924616482484310464", "timestamp": "2025-05-20T00:01:41+00:00", "timestamp_unix": 1747699301, "type": "comment", "text": "@Superaustin152 @RomanceFGC He got 1 game. Outside if that hes always <PERSON>’s sidekick/partner instead of his own separate Spider-Man", "context": {"type": "tweet", "id": "1924604949234651609", "text": "@CShelby316 @RomanceFGC He's never is you're the one saying he is that and the dumb people who hate <PERSON> \nThere's always other characters who will be around him it doesn't mean he's not showcased.\nHe got his own game.", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924654946948981081", "timestamp": "2025-05-20T02:34:32+00:00", "timestamp_unix": 1747708472, "type": "comment", "text": "@Superaustin152 Instead of <PERSON><PERSON>ey PS4 peter it should have been <PERSON>, same with <PERSON><PERSON>s, MCU. Hell he doesnt even have a live action solo movie. <PERSON> got one before <PERSON><PERSON><PERSON>. Its crazy that <PERSON> came and passed on his mantle forever and <PERSON> cant bother to retire", "context": {"type": "tweet", "id": "1924629173198274878", "text": "@CShelby316 It takes time to make stuff, and he's never <PERSON>'s sidekick, that's <PERSON><PERSON><PERSON>.\nAnd they can be partners nothing wrong with a team up", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1922902155700158471", "tweet_id": "1924655617622306971", "timestamp": "2025-05-20T02:37:12+00:00", "timestamp_unix": 1747708632, "type": "comment", "text": "@Superaustin152 What about the DCAU? Injustice? Snyderverse? Absolute? Was <PERSON> the main one?", "context": {"type": "tweet", "id": "1924628841370112433", "text": "@CShelby316 Not evey hero but yes <PERSON> shows up multiple times <PERSON> and <PERSON> are only in one ep", "author_id": "731999953665576960", "author_username": "Superaustin152"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 3}}]}