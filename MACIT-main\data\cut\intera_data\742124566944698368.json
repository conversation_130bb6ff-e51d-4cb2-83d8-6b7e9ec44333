{"user_id": "742124566944698368", "interactions_count": 16, "interactions": [{"conversation_id": "1910101734183035082", "tweet_id": "1910116850949898479", "timestamp": "2025-04-09T23:45:20+00:00", "timestamp_unix": 1744242320, "type": "comment", "text": "@TheAtlantic @GrahamDavidA https://t.co/0T8VTnnOY5", "context": {"type": "tweet", "id": "1910320170817511571", "text": "Complete morons is what's going on here. They are all idiots with a poor understanding of their roles. It's a bad reality TV show but really dangerous", "author_id": "1028318451331260416", "author_username": "Scoobie8657"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1911483764577873992", "tweet_id": "1912019720620151247", "timestamp": "2025-04-15T05:46:39+00:00", "timestamp_unix": 1744695999, "type": "comment", "text": "@BRICSinfo https://t.co/mANbAwsO6K", "context": {"type": "tweet", "id": "1912258729665257578", "text": "<PERSON><PERSON> o dele", "author_id": "1844401782325198854", "author_username": "hoaxisland"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1912122258559992188", "tweet_id": "1912246048602615872", "timestamp": "2025-04-15T20:46:00+00:00", "timestamp_unix": 1744749960, "type": "comment", "text": "@nypost https://t.co/OfPJQifjGm", "context": {"type": "tweet", "id": "1919160654130229536", "text": "China might be producing cartoonish AI videos of Americans working in sweat shops, but it inspires an important debate.\n\nWhat would we prefer as Americans: over-worked laborers halfway across the world (many of which are children), or overweight workers here at home?\n\nWhich problem would we have more power to solve? Which country do you trust more to improve labor standards and ensure safe and healthy working environments?", "author_id": "1596268726227173378", "author_username": "CorbanQualls"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1915966146416722309", "tweet_id": "1916024601597120632", "timestamp": "2025-04-26T07:00:37+00:00", "timestamp_unix": **********, "type": "comment", "text": "@FoxNews @GordonGChang https://t.co/gkIPTMI48S", "context": {"type": "tweet", "id": "1916339359290139054", "text": "LOL nice try", "author_id": "1451034243421904896", "author_username": "HorvatKen"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1919320821165658263", "tweet_id": "1919343302064980085", "timestamp": "2025-05-05T10:47:57+00:00", "timestamp_unix": **********, "type": "comment", "text": "@TIME https://t.co/4dgY3Wa3Ui", "context": {"type": "tweet", "id": "1919404102431170996", "text": "Exactly. \n\nIf other countries retaliate and impose a similar tariff on US made movies, we have more to lose for film is one of the industries where we have a positive trade balance with the world. \n\n#Trump can mess it up.", "author_id": "3723604454", "author_username": "US_Latino"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 96}}, {"conversation_id": "1920815090200088694", "tweet_id": "1921102943752749296", "timestamp": "2025-05-10T07:20:08+00:00", "timestamp_unix": 1746861608, "type": "comment", "text": "@CNN https://t.co/8BbvKrLh2d", "context": {"type": "tweet", "id": "1920935786188263480", "text": "🤣🤣🤣", "author_id": "132719455", "author_username": "CtnCndValentine"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1920815056943566851", "tweet_id": "1921104583880802667", "timestamp": "2025-05-10T07:26:39+00:00", "timestamp_unix": 1746861999, "type": "comment", "text": "@cnnbrk https://t.co/wnCcfSekm7", "context": {"type": "tweet", "id": "1921068087048106136", "text": "Folding like a bedsheet lmao", "author_id": "969172081148678145", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1920817322614222902", "tweet_id": "1921136303627747670", "timestamp": "2025-05-10T09:32:42+00:00", "timestamp_unix": 1746869562, "type": "comment", "text": "@ArtCandee https://t.co/sK1ITSm3WB", "context": {"type": "tweet", "id": "1922599816787378314", "text": "Trump is provoking war and depriving the people of their rights", "author_id": "1904061325187354624", "author_username": "sam123456160523"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1921402895271964940", "tweet_id": "1921463183912456331", "timestamp": "2025-05-11T07:11:36+00:00", "timestamp_unix": 1746947496, "type": "comment", "text": "@FoxNews https://t.co/7y4FHaum0p", "context": {"type": "tweet", "id": "1921747212993769474", "text": "https://t.co/d4QJ6wGXT1", "author_id": "1516885871093424128", "author_username": "spacecardealers"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1921399279643455783", "tweet_id": "1921476176196915648", "timestamp": "2025-05-11T08:03:14+00:00", "timestamp_unix": 1746950594, "type": "comment", "text": "@nypost https://t.co/6Wx73PAwRD", "context": {"type": "tweet", "id": "1921602929804317083", "text": "@Danielkalombo tout va si vite que votre thread est déjà dépassé : https://t.co/rggvR25rLq", "author_id": "1786712973269942272", "author_username": "wilhelm_ma49370"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921399279643455783", "tweet_id": "1921476243385463169", "timestamp": "2025-05-11T08:03:30+00:00", "timestamp_unix": 1746950610, "type": "comment", "text": "@nypost https://t.co/q1VmwgOH4f", "context": {"type": "tweet", "id": "1921602929804317083", "text": "@Danielkalombo tout va si vite que votre thread est déjà dépassé : https://t.co/rggvR25rLq", "author_id": "1786712973269942272", "author_username": "wilhelm_ma49370"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921392329715278109", "tweet_id": "1921476384720826799", "timestamp": "2025-05-11T08:04:04+00:00", "timestamp_unix": 1746950644, "type": "comment", "text": "@foxnewspolitics https://t.co/wlsuVrEe6k", "context": {"type": "tweet", "id": "1921427672304922815", "text": "https://t.co/tUnT0bjULG", "author_id": "2605676312", "author_username": "dannyyy33"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1921892614543937536", "tweet_id": "1921909134653325546", "timestamp": "2025-05-12T12:43:39+00:00", "timestamp_unix": 1747053819, "type": "comment", "text": "@TIME https://t.co/KgnhQjdHeW", "context": {"type": "tweet", "id": "1921892614543937536", "text": "原始推文内容不可用", "author_id": "14293310", "author_username": "TIME"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921924612893311059", "tweet_id": "1922127031703110042", "timestamp": "2025-05-13T03:09:30+00:00", "timestamp_unix": 1747105770, "type": "comment", "text": "@FoxNews https://t.co/oc2rMAEU0p", "context": {"type": "tweet", "id": "1921954586694230276", "text": "Ohh look - trying to harm China was a bad idea - they are good for the American economy - imagine that.", "author_id": "1800162803522695168", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1922140498086523106", "tweet_id": "1922161398584316233", "timestamp": "2025-05-13T05:26:04+00:00", "timestamp_unix": 1747113964, "type": "comment", "text": "@TIME https://t.co/Syc5EoJ6xn", "context": {"type": "tweet", "id": "1922470716546355258", "text": "More leftist retardation. Who actually believes these fools?", "author_id": "1682830104584503301", "author_username": "swterry911"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1922153949479510029", "tweet_id": "1922185960923521193", "timestamp": "2025-05-13T07:03:40+00:00", "timestamp_unix": 1747119820, "type": "comment", "text": "@cnnbrk https://t.co/qTMPxCzWOw", "context": {"type": "tweet", "id": "1922451457535545393", "text": "Fuck You @POTUS", "author_id": "1857461012334624768", "author_username": "Razorboi1320"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}]}