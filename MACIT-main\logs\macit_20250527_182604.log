2025-05-27 18:26:04,849 - test_10_users - INFO - 开始测试10个用户的意图分析...
2025-05-27 18:26:06,668 - test_10_users - INFO - MACIT框架初始化成功
2025-05-27 18:26:06,670 - test_10_users - INFO - 将测试以下10个用户: ['1004599646080131072', '1021702922927669249', '1027405890314358784', '1034308064', '1037663718686031879', '1038128766575202306', '1058187164', '1064220744677232642', '1070439999517024257', '1070616509423280128']
2025-05-27 18:26:06,671 - test_10_users - INFO - 正在分析用户 1004599646080131072 (1/10)...
2025-05-27 18:27:39,275 - test_10_users - INFO - 用户 1004599646080131072 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1004599646080131072\interaction_0.json
2025-05-27 18:27:39,276 - test_10_users - INFO - 正在分析用户 1021702922927669249 (2/10)...
2025-05-27 18:29:09,799 - test_10_users - INFO - 用户 1021702922927669249 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1021702922927669249\interaction_0.json
2025-05-27 18:29:09,800 - test_10_users - INFO - 正在分析用户 1027405890314358784 (3/10)...
2025-05-27 18:30:46,424 - test_10_users - INFO - 用户 1027405890314358784 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1027405890314358784\interaction_0.json
2025-05-27 18:30:46,425 - test_10_users - INFO - 正在分析用户 1034308064 (4/10)...
2025-05-27 18:32:13,271 - test_10_users - INFO - 用户 1034308064 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1034308064\interaction_0.json
2025-05-27 18:32:13,271 - test_10_users - INFO - 正在分析用户 1037663718686031879 (5/10)...
2025-05-27 18:33:47,068 - test_10_users - INFO - 用户 1037663718686031879 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1037663718686031879\interaction_0.json
2025-05-27 18:33:47,069 - test_10_users - INFO - 正在分析用户 1038128766575202306 (6/10)...
2025-05-27 18:35:29,759 - test_10_users - INFO - 用户 1038128766575202306 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1038128766575202306\interaction_0.json
2025-05-27 18:35:29,759 - test_10_users - INFO - 正在分析用户 1058187164 (7/10)...
2025-05-27 18:35:30,004 - test_10_users - ERROR - 分析用户 1058187164 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 18:35:30,005 - test_10_users - INFO - 正在分析用户 1064220744677232642 (8/10)...
2025-05-27 18:35:30,136 - test_10_users - ERROR - 分析用户 1064220744677232642 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 18:35:30,136 - test_10_users - INFO - 正在分析用户 1070439999517024257 (9/10)...
2025-05-27 18:35:30,255 - test_10_users - ERROR - 分析用户 1070439999517024257 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 18:35:30,256 - test_10_users - INFO - 正在分析用户 1070616509423280128 (10/10)...
2025-05-27 18:35:30,368 - test_10_users - ERROR - 分析用户 1070616509423280128 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 18:35:30,369 - test_10_users - INFO - 所有用户分析完成，正在整理结果...
