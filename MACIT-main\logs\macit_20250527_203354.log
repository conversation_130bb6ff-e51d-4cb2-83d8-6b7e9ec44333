2025-05-27 20:33:54,692 - main - INFO - 正在初始化MACIT框架（细粒度结构化意图标签模型）...
2025-05-27 20:33:57,405 - main - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 20:33:57,407 - main - INFO - 发现 500 个用户数据文件
2025-05-27 20:33:57,408 - main - INFO - 将要分析 500 个用户的细粒度结构化意图
2025-05-27 20:33:57,408 - main - INFO - 正在分析用户 1004599646080131072 (1/500)...
2025-05-27 20:37:20,055 - main - INFO - 用户 1004599646080131072 的细粒度意图分析完成
2025-05-27 20:37:22,072 - main - INFO - 正在分析用户 1021702922927669249 (2/500)...
2025-05-27 20:40:19,155 - main - INFO - 用户 1021702922927669249 的细粒度意图分析完成
2025-05-27 20:40:21,158 - main - INFO - 正在分析用户 1027405890314358784 (3/500)...
2025-05-27 20:43:24,178 - main - INFO - 用户 1027405890314358784 的细粒度意图分析完成
2025-05-27 20:43:26,183 - main - INFO - 正在分析用户 1034308064 (4/500)...
2025-05-27 20:46:17,985 - main - INFO - 用户 1034308064 的细粒度意图分析完成
2025-05-27 20:46:19,993 - main - INFO - 正在分析用户 1037663718686031879 (5/500)...
2025-05-27 20:49:43,882 - main - INFO - 用户 1037663718686031879 的细粒度意图分析完成
2025-05-27 20:49:45,892 - main - INFO - 正在分析用户 1038128766575202306 (6/500)...
2025-05-27 20:52:54,859 - main - INFO - 用户 1038128766575202306 的细粒度意图分析完成
2025-05-27 20:52:56,863 - main - INFO - 正在分析用户 1058187164 (7/500)...
2025-05-27 20:52:57,083 - main - ERROR - 分析用户 1058187164 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:52:59,097 - main - INFO - 正在分析用户 1064220744677232642 (8/500)...
2025-05-27 20:52:59,201 - main - ERROR - 分析用户 1064220744677232642 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:01,215 - main - INFO - 正在分析用户 1070439999517024257 (9/500)...
2025-05-27 20:53:01,329 - main - ERROR - 分析用户 1070439999517024257 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:03,346 - main - INFO - 正在分析用户 1070616509423280128 (10/500)...
2025-05-27 20:53:03,495 - main - ERROR - 分析用户 1070616509423280128 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:05,513 - main - INFO - 正在分析用户 1071564698498162688 (11/500)...
2025-05-27 20:53:05,706 - main - ERROR - 分析用户 1071564698498162688 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:07,713 - main - INFO - 正在分析用户 1092240466135957507 (12/500)...
2025-05-27 20:53:07,834 - main - ERROR - 分析用户 1092240466135957507 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:09,837 - main - INFO - 正在分析用户 1100624286 (13/500)...
2025-05-27 20:53:09,967 - main - ERROR - 分析用户 1100624286 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:11,969 - main - INFO - 正在分析用户 1104645412445540352 (14/500)...
2025-05-27 20:53:12,098 - main - ERROR - 分析用户 1104645412445540352 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:14,101 - main - INFO - 正在分析用户 1106852624438042625 (15/500)...
2025-05-27 20:53:14,229 - main - ERROR - 分析用户 1106852624438042625 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:16,233 - main - INFO - 正在分析用户 1110335417516085253 (16/500)...
2025-05-27 20:53:16,402 - main - ERROR - 分析用户 1110335417516085253 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:18,418 - main - INFO - 正在分析用户 1120219427277025280 (17/500)...
2025-05-27 20:53:18,546 - main - ERROR - 分析用户 1120219427277025280 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:20,549 - main - INFO - 正在分析用户 1127520974918778880 (18/500)...
2025-05-27 20:53:20,689 - main - ERROR - 分析用户 1127520974918778880 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:22,695 - main - INFO - 正在分析用户 112932815 (19/500)...
2025-05-27 20:53:22,833 - main - ERROR - 分析用户 112932815 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:24,837 - main - INFO - 正在分析用户 1135014749949743105 (20/500)...
2025-05-27 20:53:24,985 - main - ERROR - 分析用户 1135014749949743105 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:26,997 - main - INFO - 正在分析用户 1139841942420893696 (21/500)...
2025-05-27 20:53:27,140 - main - ERROR - 分析用户 1139841942420893696 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:29,155 - main - INFO - 正在分析用户 1147084446 (22/500)...
2025-05-27 20:53:29,306 - main - ERROR - 分析用户 1147084446 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:31,321 - main - INFO - 正在分析用户 1148955772077789185 (23/500)...
2025-05-27 20:53:31,475 - main - ERROR - 分析用户 1148955772077789185 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:33,479 - main - INFO - 正在分析用户 1157834616 (24/500)...
2025-05-27 20:53:33,635 - main - ERROR - 分析用户 1157834616 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:35,647 - main - INFO - 正在分析用户 1158472312267649025 (25/500)...
2025-05-27 20:53:35,808 - main - ERROR - 分析用户 1158472312267649025 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:37,818 - main - INFO - 正在分析用户 115911056 (26/500)...
2025-05-27 20:53:38,180 - main - ERROR - 分析用户 115911056 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:40,185 - main - INFO - 正在分析用户 1167765353415487488 (27/500)...
2025-05-27 20:53:40,346 - main - ERROR - 分析用户 1167765353415487488 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:42,352 - main - INFO - 正在分析用户 1173914482071879680 (28/500)...
2025-05-27 20:53:42,531 - main - ERROR - 分析用户 1173914482071879680 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:44,538 - main - INFO - 正在分析用户 1175897186296401920 (29/500)...
2025-05-27 20:53:44,821 - main - ERROR - 分析用户 1175897186296401920 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:46,827 - main - INFO - 正在分析用户 1178590216870744065 (30/500)...
2025-05-27 20:53:46,981 - main - ERROR - 分析用户 1178590216870744065 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:48,999 - main - INFO - 正在分析用户 1203768623019380737 (31/500)...
2025-05-27 20:53:49,150 - main - ERROR - 分析用户 1203768623019380737 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:51,159 - main - INFO - 正在分析用户 122064004 (32/500)...
2025-05-27 20:53:51,309 - main - ERROR - 分析用户 122064004 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:53,313 - main - INFO - 正在分析用户 1226717447626973184 (33/500)...
2025-05-27 20:53:53,484 - main - ERROR - 分析用户 1226717447626973184 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:55,500 - main - INFO - 正在分析用户 1230412101102882816 (34/500)...
2025-05-27 20:53:55,689 - main - ERROR - 分析用户 1230412101102882816 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:57,697 - main - INFO - 正在分析用户 1230695226831826946 (35/500)...
2025-05-27 20:53:57,928 - main - ERROR - 分析用户 1230695226831826946 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:53:59,938 - main - INFO - 正在分析用户 1233798506818621440 (36/500)...
2025-05-27 20:54:00,123 - main - ERROR - 分析用户 1233798506818621440 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:02,126 - main - INFO - 正在分析用户 1239381340849463298 (37/500)...
2025-05-27 20:54:02,297 - main - ERROR - 分析用户 1239381340849463298 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:04,305 - main - INFO - 正在分析用户 1239701188909596674 (38/500)...
2025-05-27 20:54:04,489 - main - ERROR - 分析用户 1239701188909596674 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:06,493 - main - INFO - 正在分析用户 1242662521049198592 (39/500)...
2025-05-27 20:54:06,669 - main - ERROR - 分析用户 1242662521049198592 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:08,681 - main - INFO - 正在分析用户 1245539854009548800 (40/500)...
2025-05-27 20:54:08,864 - main - ERROR - 分析用户 1245539854009548800 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:10,869 - main - INFO - 正在分析用户 1250865635510366216 (41/500)...
2025-05-27 20:54:11,075 - main - ERROR - 分析用户 1250865635510366216 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:13,081 - main - INFO - 正在分析用户 1252788976630427648 (42/500)...
2025-05-27 20:54:13,262 - main - ERROR - 分析用户 1252788976630427648 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:15,278 - main - INFO - 正在分析用户 1254011152545218563 (43/500)...
2025-05-27 20:54:15,469 - main - ERROR - 分析用户 1254011152545218563 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:17,475 - main - INFO - 正在分析用户 1266949682929913857 (44/500)...
2025-05-27 20:54:17,661 - main - ERROR - 分析用户 1266949682929913857 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:19,670 - main - INFO - 正在分析用户 126798663 (45/500)...
2025-05-27 20:54:19,859 - main - ERROR - 分析用户 126798663 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:21,863 - main - INFO - 正在分析用户 1272328745110474752 (46/500)...
2025-05-27 20:54:22,047 - main - ERROR - 分析用户 1272328745110474752 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:24,049 - main - INFO - 正在分析用户 1280249208788975621 (47/500)...
2025-05-27 20:54:24,240 - main - ERROR - 分析用户 1280249208788975621 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:26,251 - main - INFO - 正在分析用户 1282241857175552000 (48/500)...
2025-05-27 20:54:26,446 - main - ERROR - 分析用户 1282241857175552000 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:28,451 - main - INFO - 正在分析用户 1286280259596427264 (49/500)...
2025-05-27 20:54:28,664 - main - ERROR - 分析用户 1286280259596427264 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:30,669 - main - INFO - 正在分析用户 1290303267616862209 (50/500)...
2025-05-27 20:54:30,870 - main - ERROR - 分析用户 1290303267616862209 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:32,881 - main - INFO - 正在分析用户 1294118365477339137 (51/500)...
2025-05-27 20:54:33,097 - main - ERROR - 分析用户 1294118365477339137 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:35,102 - main - INFO - 正在分析用户 1296036637902807041 (52/500)...
2025-05-27 20:54:35,311 - main - ERROR - 分析用户 1296036637902807041 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:37,314 - main - INFO - 正在分析用户 1303819115522650112 (53/500)...
2025-05-27 20:54:37,523 - main - ERROR - 分析用户 1303819115522650112 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:39,530 - main - INFO - 正在分析用户 1303940008634540032 (54/500)...
2025-05-27 20:54:39,734 - main - ERROR - 分析用户 1303940008634540032 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:41,742 - main - INFO - 正在分析用户 1304390820024299522 (55/500)...
2025-05-27 20:54:42,055 - main - ERROR - 分析用户 1304390820024299522 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:44,063 - main - INFO - 正在分析用户 1308269036719939584 (56/500)...
2025-05-27 20:54:44,278 - main - ERROR - 分析用户 1308269036719939584 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:46,287 - main - INFO - 正在分析用户 1314023856663912448 (57/500)...
2025-05-27 20:54:46,532 - main - ERROR - 分析用户 1314023856663912448 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:48,542 - main - INFO - 正在分析用户 1318836473727131648 (58/500)...
2025-05-27 20:54:48,781 - main - ERROR - 分析用户 1318836473727131648 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:50,787 - main - INFO - 正在分析用户 1322486724178505728 (59/500)...
2025-05-27 20:54:50,997 - main - ERROR - 分析用户 1322486724178505728 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:53,002 - main - INFO - 正在分析用户 132726058 (60/500)...
2025-05-27 20:54:53,241 - main - ERROR - 分析用户 132726058 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:55,245 - main - INFO - 正在分析用户 1329448526926008324 (61/500)...
2025-05-27 20:54:55,486 - main - ERROR - 分析用户 1329448526926008324 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:57,487 - main - INFO - 正在分析用户 1332012152782090246 (62/500)...
2025-05-27 20:54:57,720 - main - ERROR - 分析用户 1332012152782090246 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:54:59,730 - main - INFO - 正在分析用户 1332477587399053313 (63/500)...
2025-05-27 20:54:59,988 - main - ERROR - 分析用户 1332477587399053313 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:01,999 - main - INFO - 正在分析用户 1345007599461101571 (64/500)...
2025-05-27 20:55:02,242 - main - ERROR - 分析用户 1345007599461101571 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:04,251 - main - INFO - 正在分析用户 1345288135807180801 (65/500)...
2025-05-27 20:55:04,488 - main - ERROR - 分析用户 1345288135807180801 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:06,503 - main - INFO - 正在分析用户 1349041470498615297 (66/500)...
2025-05-27 20:55:06,750 - main - ERROR - 分析用户 1349041470498615297 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:08,763 - main - INFO - 正在分析用户 1349371486667157505 (67/500)...
2025-05-27 20:55:09,017 - main - ERROR - 分析用户 1349371486667157505 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:11,033 - main - INFO - 正在分析用户 1358591479543787529 (68/500)...
2025-05-27 20:55:11,300 - main - ERROR - 分析用户 1358591479543787529 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:13,310 - main - INFO - 正在分析用户 1359045071677624322 (69/500)...
2025-05-27 20:55:13,607 - main - ERROR - 分析用户 1359045071677624322 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:15,609 - main - INFO - 正在分析用户 1360017398154289154 (70/500)...
2025-05-27 20:55:15,912 - main - ERROR - 分析用户 1360017398154289154 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:17,920 - main - INFO - 正在分析用户 1361185819785113602 (71/500)...
2025-05-27 20:55:18,216 - main - ERROR - 分析用户 1361185819785113602 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:20,221 - main - INFO - 正在分析用户 1371858710990061570 (72/500)...
2025-05-27 20:55:20,670 - main - ERROR - 分析用户 1371858710990061570 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:22,676 - main - INFO - 正在分析用户 1378474173417226243 (73/500)...
2025-05-27 20:55:23,095 - main - ERROR - 分析用户 1378474173417226243 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:25,100 - main - INFO - 正在分析用户 1384353601800196097 (74/500)...
2025-05-27 20:55:25,395 - main - ERROR - 分析用户 1384353601800196097 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:27,403 - main - INFO - 正在分析用户 1386025116547563521 (75/500)...
2025-05-27 20:55:27,784 - main - ERROR - 分析用户 1386025116547563521 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:29,791 - main - INFO - 正在分析用户 1388148296183517187 (76/500)...
2025-05-27 20:55:30,077 - main - ERROR - 分析用户 1388148296183517187 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:32,089 - main - INFO - 正在分析用户 1390210221172158465 (77/500)...
2025-05-27 20:55:32,381 - main - ERROR - 分析用户 1390210221172158465 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:34,391 - main - INFO - 正在分析用户 1392475958247006212 (78/500)...
2025-05-27 20:55:34,727 - main - ERROR - 分析用户 1392475958247006212 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:36,736 - main - INFO - 正在分析用户 1394273888637394945 (79/500)...
2025-05-27 20:55:37,052 - main - ERROR - 分析用户 1394273888637394945 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-27 20:55:39,066 - main - INFO - 正在分析用户 1397513095367561217 (80/500)...
