2025-05-27 21:19:44,452 - test_10_users - INFO - 开始测试10个用户的意图分析...
2025-05-27 21:19:44,486 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:19:44,486 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:19:44,488 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:19:44,488 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:19:44,489 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:44,489 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:44,490 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:44,490 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:45,407 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:45,407 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,191 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:19:46,191 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:19:46,192 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,192 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,192 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:46,192 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:46,976 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,976 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,978 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:19:46,978 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:19:46,978 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,978 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:46,979 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:46,979 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:47,538 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:47,538 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:47,539 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:19:47,539 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:19:47,540 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:47,540 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:47,540 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:47,540 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:19:48,079 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:48,079 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025-05-27 21:19:48,081 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:19:48,081 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:19:48,081 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:19:48,081 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:19:48,081 - test_10_users - INFO - MACIT框架初始化成功
2025-05-27 21:19:48,085 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:19:48,085 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:19:48,085 - test_10_users - INFO - 将测试以下10个用户: ['1004599646080131072', '1021702922927669249', '1027405890314358784', '1034308064', '1037663718686031879', '1038128766575202306', '1058187164', '1064220744677232642', '1070439999517024257', '1070616509423280128']
2025-05-27 21:19:48,087 - test_10_users - INFO - 正在分析用户 1004599646080131072 (1/10)...
2025-05-27 21:19:48,087 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,087 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,089 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:48,089 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:48,089 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,089 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,090 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,090 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,092 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:48,092 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:19:48,093 - data_utils - WARNING - 未找到用户 1004599646080131072 的画像
2025-05-27 21:19:48,093 - data_utils - WARNING - 未找到用户 1004599646080131072 的画像
2025-05-27 21:19:48,093 - macit_framework - WARNING - 未找到用户 1004599646080131072 的画像，使用空画像
2025-05-27 21:19:48,093 - macit_framework - WARNING - 未找到用户 1004599646080131072 的画像，使用空画像
2025-05-27 21:19:48,094 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,094 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,095 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,095 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,866 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:48,868 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:48,869 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '2e1f6715-ef2e-9a01-901d-a06155040465', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,878 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2e1f6715-ef2e-9a01-901d-a06155040465', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,878 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2e1f6715-ef2e-9a01-901d-a06155040465', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,880 - test_10_users - ERROR - 分析用户 1004599646080131072 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2e1f6715-ef2e-9a01-901d-a06155040465', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,882 - test_10_users - INFO - 正在分析用户 1021702922927669249 (2/10)...
2025-05-27 21:19:48,883 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,883 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,884 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:48,884 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:48,885 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,885 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,886 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,886 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,888 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:48,888 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:19:48,888 - data_utils - WARNING - 未找到用户 1021702922927669249 的画像
2025-05-27 21:19:48,888 - data_utils - WARNING - 未找到用户 1021702922927669249 的画像
2025-05-27 21:19:48,889 - macit_framework - WARNING - 未找到用户 1021702922927669249 的画像，使用空画像
2025-05-27 21:19:48,889 - macit_framework - WARNING - 未找到用户 1021702922927669249 的画像，使用空画像
2025-05-27 21:19:48,889 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,889 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,890 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,890 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,934 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:48,936 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:48,936 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '286705d2-c1c1-9985-b5de-e66a20f3022c', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,943 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '286705d2-c1c1-9985-b5de-e66a20f3022c', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,943 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '286705d2-c1c1-9985-b5de-e66a20f3022c', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,945 - test_10_users - ERROR - 分析用户 1021702922927669249 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '286705d2-c1c1-9985-b5de-e66a20f3022c', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:48,947 - test_10_users - INFO - 正在分析用户 1027405890314358784 (3/10)...
2025-05-27 21:19:48,948 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,948 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:48,950 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:48,950 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:48,950 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,950 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,951 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,951 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:48,953 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:48,953 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:19:48,953 - data_utils - WARNING - 未找到用户 1027405890314358784 的画像
2025-05-27 21:19:48,953 - data_utils - WARNING - 未找到用户 1027405890314358784 的画像
2025-05-27 21:19:48,954 - macit_framework - WARNING - 未找到用户 1027405890314358784 的画像，使用空画像
2025-05-27 21:19:48,954 - macit_framework - WARNING - 未找到用户 1027405890314358784 的画像，使用空画像
2025-05-27 21:19:48,954 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,954 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:48,955 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,955 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:48,997 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:48,999 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,000 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '47e3ff89-e1ea-9058-a2fc-8bdcac3cf318', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,006 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '47e3ff89-e1ea-9058-a2fc-8bdcac3cf318', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,006 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '47e3ff89-e1ea-9058-a2fc-8bdcac3cf318', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,008 - test_10_users - ERROR - 分析用户 1027405890314358784 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '47e3ff89-e1ea-9058-a2fc-8bdcac3cf318', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,009 - test_10_users - INFO - 正在分析用户 1034308064 (4/10)...
2025-05-27 21:19:49,010 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,010 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,012 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:49,012 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:49,013 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,013 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,014 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,014 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,015 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:49,015 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:19:49,016 - data_utils - WARNING - 未找到用户 1034308064 的画像
2025-05-27 21:19:49,016 - data_utils - WARNING - 未找到用户 1034308064 的画像
2025-05-27 21:19:49,017 - macit_framework - WARNING - 未找到用户 1034308064 的画像，使用空画像
2025-05-27 21:19:49,017 - macit_framework - WARNING - 未找到用户 1034308064 的画像，使用空画像
2025-05-27 21:19:49,018 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,018 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,019 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,019 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,060 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,064 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,065 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '9b4eeaaa-989e-93fa-ae3a-1a5909032e2b', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,070 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '9b4eeaaa-989e-93fa-ae3a-1a5909032e2b', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,070 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '9b4eeaaa-989e-93fa-ae3a-1a5909032e2b', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,071 - test_10_users - ERROR - 分析用户 1034308064 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '9b4eeaaa-989e-93fa-ae3a-1a5909032e2b', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,073 - test_10_users - INFO - 正在分析用户 1037663718686031879 (5/10)...
2025-05-27 21:19:49,074 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,074 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,076 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:49,076 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:49,077 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,077 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,079 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,079 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,081 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:49,081 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:19:49,081 - data_utils - WARNING - 未找到用户 1037663718686031879 的画像
2025-05-27 21:19:49,081 - data_utils - WARNING - 未找到用户 1037663718686031879 的画像
2025-05-27 21:19:49,082 - macit_framework - WARNING - 未找到用户 1037663718686031879 的画像，使用空画像
2025-05-27 21:19:49,082 - macit_framework - WARNING - 未找到用户 1037663718686031879 的画像，使用空画像
2025-05-27 21:19:49,082 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,082 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,083 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,083 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,126 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,128 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,129 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '7c1794f1-259b-9d6c-babc-8703249d3bbf', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,135 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '7c1794f1-259b-9d6c-babc-8703249d3bbf', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,135 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '7c1794f1-259b-9d6c-babc-8703249d3bbf', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,137 - test_10_users - ERROR - 分析用户 1037663718686031879 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '7c1794f1-259b-9d6c-babc-8703249d3bbf', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,138 - test_10_users - INFO - 正在分析用户 1038128766575202306 (6/10)...
2025-05-27 21:19:49,140 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,140 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,141 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:49,141 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:49,142 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,142 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,143 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,143 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,145 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:49,145 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:19:49,146 - data_utils - WARNING - 未找到用户 1038128766575202306 的画像
2025-05-27 21:19:49,146 - data_utils - WARNING - 未找到用户 1038128766575202306 的画像
2025-05-27 21:19:49,147 - macit_framework - WARNING - 未找到用户 1038128766575202306 的画像，使用空画像
2025-05-27 21:19:49,147 - macit_framework - WARNING - 未找到用户 1038128766575202306 的画像，使用空画像
2025-05-27 21:19:49,148 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,148 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,149 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,149 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,191 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,193 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,194 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '67d749a6-ff86-90a8-8992-90e3bd758fda', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,199 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '67d749a6-ff86-90a8-8992-90e3bd758fda', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,199 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '67d749a6-ff86-90a8-8992-90e3bd758fda', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,200 - test_10_users - ERROR - 分析用户 1038128766575202306 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '67d749a6-ff86-90a8-8992-90e3bd758fda', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,202 - test_10_users - INFO - 正在分析用户 1058187164 (7/10)...
2025-05-27 21:19:49,203 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,203 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,205 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:49,205 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:49,206 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,206 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,207 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,207 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,209 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:49,209 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:19:49,209 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-27 21:19:49,209 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-27 21:19:49,210 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-27 21:19:49,210 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-27 21:19:49,211 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,211 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,211 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,211 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,257 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,258 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,259 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '14e88ed8-867b-9c8f-b310-11b65c661eff', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,264 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '14e88ed8-867b-9c8f-b310-11b65c661eff', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,264 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '14e88ed8-867b-9c8f-b310-11b65c661eff', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,265 - test_10_users - ERROR - 分析用户 1058187164 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '14e88ed8-867b-9c8f-b310-11b65c661eff', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,267 - test_10_users - INFO - 正在分析用户 1064220744677232642 (8/10)...
2025-05-27 21:19:49,269 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,269 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,270 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:49,270 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:49,271 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,271 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,272 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,272 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,274 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:49,274 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:19:49,275 - data_utils - WARNING - 未找到用户 1064220744677232642 的画像
2025-05-27 21:19:49,275 - data_utils - WARNING - 未找到用户 1064220744677232642 的画像
2025-05-27 21:19:49,275 - macit_framework - WARNING - 未找到用户 1064220744677232642 的画像，使用空画像
2025-05-27 21:19:49,275 - macit_framework - WARNING - 未找到用户 1064220744677232642 的画像，使用空画像
2025-05-27 21:19:49,276 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,276 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,277 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,277 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,322 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,324 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,324 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '40305bd3-40c7-9dce-9991-7502d9145aea', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,330 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '40305bd3-40c7-9dce-9991-7502d9145aea', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,330 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '40305bd3-40c7-9dce-9991-7502d9145aea', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,332 - test_10_users - ERROR - 分析用户 1064220744677232642 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '40305bd3-40c7-9dce-9991-7502d9145aea', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,333 - test_10_users - INFO - 正在分析用户 1070439999517024257 (9/10)...
2025-05-27 21:19:49,334 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,334 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,336 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:49,336 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:49,337 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,337 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,337 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,337 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,338 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:49,338 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:19:49,339 - data_utils - WARNING - 未找到用户 1070439999517024257 的画像
2025-05-27 21:19:49,339 - data_utils - WARNING - 未找到用户 1070439999517024257 的画像
2025-05-27 21:19:49,339 - macit_framework - WARNING - 未找到用户 1070439999517024257 的画像，使用空画像
2025-05-27 21:19:49,339 - macit_framework - WARNING - 未找到用户 1070439999517024257 的画像，使用空画像
2025-05-27 21:19:49,340 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,340 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,341 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,341 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,382 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,384 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,384 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '09c12100-1751-9209-9e3c-c165be5de509', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,389 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '09c12100-1751-9209-9e3c-c165be5de509', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,389 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '09c12100-1751-9209-9e3c-c165be5de509', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,390 - test_10_users - ERROR - 分析用户 1070439999517024257 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '09c12100-1751-9209-9e3c-c165be5de509', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,391 - test_10_users - INFO - 正在分析用户 1070616509423280128 (10/10)...
2025-05-27 21:19:49,392 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,392 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:19:49,393 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:49,393 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:49,394 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,394 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,395 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,395 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:19:49,397 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:49,397 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:19:49,397 - data_utils - WARNING - 未找到用户 1070616509423280128 的画像
2025-05-27 21:19:49,397 - data_utils - WARNING - 未找到用户 1070616509423280128 的画像
2025-05-27 21:19:49,398 - macit_framework - WARNING - 未找到用户 1070616509423280128 的画像，使用空画像
2025-05-27 21:19:49,398 - macit_framework - WARNING - 未找到用户 1070616509423280128 的画像，使用空画像
2025-05-27 21:19:49,398 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,398 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:19:49,399 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,399 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:19:49,443 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-27 21:19:49,444 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x000001B9826E2140>
2025-05-27 21:19:49,445 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'requestId': '2752c8cd-ff9a-93da-9e5d-4c373a8aa162', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,450 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2752c8cd-ff9a-93da-9e5d-4c373a8aa162', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,450 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2752c8cd-ff9a-93da-9e5d-4c373a8aa162', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,451 - test_10_users - ERROR - 分析用户 1070616509423280128 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'requestId': '2752c8cd-ff9a-93da-9e5d-4c373a8aa162', 'code': 'InvalidParameter', 'message': 'No static resource api/v1/aigc/text-generation/generation/chat/completions.'}
2025-05-27 21:19:49,453 - test_10_users - INFO - 所有用户分析完成，正在整理结果...
2025-05-27 21:19:49,455 - test_10_users - INFO - 格式化结果已保存至: output\test_10_users_results.txt
