{"user_id": "28822067", "interactions_count": 15, "interactions": [{"conversation_id": "1921137832606761251", "tweet_id": "1921266840585543802", "timestamp": "2025-05-10T18:11:24+00:00", "timestamp_unix": 1746900684, "type": "comment", "text": "@borgh<PERSON>_claudio @KevGori Sa che io a volte ho l’impressione che lei non capisca l’italiano? Io non ho scritto quello che ha capito lei. Non passerà troppo tempo vicino a dei condizionatori?", "context": {"type": "tweet", "id": "1921199838684750319", "text": "@LucaBizzarri @Kev<PERSON><PERSON>ri, lo so che lei è strano ma anche per lei trovo difficile che il suo cane, per simile a lei che possa essere, riesca a strusciarsi su piccioni morti mentre si trova in un trasportino. A meno che non li vada a cercare lei per poi introdurli nello sportellino.", "author_id": "337767301", "author_username": "borgh<PERSON>_claudio"}, "metrics": {"retweet_count": 2, "reply_count": 11, "like_count": 70, "quote_count": 0, "view_count": 2519}}, {"conversation_id": "1921137832606761251", "tweet_id": "1921479870346866771", "timestamp": "2025-05-11T08:17:55+00:00", "timestamp_unix": 1746951475, "type": "comment", "text": "@borgh<PERSON>_claudio @KevGori Io non so cosa le abbiano dato, ma il cane se deve pisciare piscia pure nel trasportino. Così se deve cagare, o vomitare. Glielo dico per esperienza. Su una cosa però ha ragione: io voglio fare il pagliaccio, nel senso che ne sono consapevole.", "context": {"type": "tweet", "id": "1921359000525824307", "text": "@LucaBizzarri @Kev<PERSON><PERSON>, r<PERSON><PERSON><PERSON>, stavo parlando di cani chiusi nei trasportini come è sempre successo per tutti i cani trasportati in aereo. Le possibilità sono tre: o quello che non capisce l'italiano è lei, o dà risposte che non c'entrano nulla e quindi ha problemi, o vuol fare il pagliaccio", "author_id": "337767301", "author_username": "borgh<PERSON>_claudio"}, "metrics": {"retweet_count": 1, "reply_count": 4, "like_count": 44, "quote_count": 0, "view_count": 1076}}, {"conversation_id": "1921137832606761251", "tweet_id": "1921817514549387569", "timestamp": "2025-05-12T06:39:35+00:00", "timestamp_unix": 1747031975, "type": "comment", "text": "@las<PERSON><PERSON> @antone<PERSON><PERSON><PERSON><PERSON> @borghi_claudio Of course, but maybe.", "context": {"type": "tweet", "id": "1921816431953428890", "text": "@antone<PERSON>di<PERSON><PERSON> @borghi_claudio @LucaBizzarri Il che è però un problema che, dal punto di vista dell'insiemistica, non ha alcuna intersezione con \"non voglio segnalare alla compagnia aerea il *dato sensibile* che sono allergico ai cani\"", "author_id": "8357342", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 505}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923297513160777857", "timestamp": "2025-05-16T08:40:34+00:00", "timestamp_unix": 1747384834, "type": "comment", "text": "@SEMPERFIDELIS73 Perdonami ti sei accorta che ho scritto un post che ti dà ragione?", "context": {"type": "tweet", "id": "1923636282111967659", "text": "ecco, stavolta ne ha detta una giusta.", "author_id": "2250752450", "author_username": "anncadom"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 259}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923298458821124476", "timestamp": "2025-05-16T08:44:20+00:00", "timestamp_unix": 1747385060, "type": "comment", "text": "@ottobrerosa Bella l’espressione “tergiculo”. Sono neologismi che nascono da passioni personali, è evidente.", "context": {"type": "tweet", "id": "1923636282111967659", "text": "ecco, stavolta ne ha detta una giusta.", "author_id": "2250752450", "author_username": "anncadom"}, "metrics": {"retweet_count": 0, "reply_count": 6, "like_count": 92, "quote_count": 0, "view_count": 3682}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923300958492061839", "timestamp": "2025-05-16T08:54:16+00:00", "timestamp_unix": 1747385656, "type": "comment", "text": "@wnamass @ottobrerosa Cioè io posso essere tergiculo. Altri no. La democrazia di voi picchiatelli.", "context": {"type": "tweet", "id": "1923300735254479296", "text": "@LucaBizzarri @ottobrerosa pessimo ancora e ancora", "author_id": "554447353", "author_username": "w<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 14, "quote_count": 0, "view_count": 1190}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923309149363790104", "timestamp": "2025-05-16T09:26:49+00:00", "timestamp_unix": 1747387609, "type": "comment", "text": "@wnamass @ottobrerosa E quale sarebbe il merito di una che ti scrive \"Stai zitto tergiculo\", genio?", "context": {"type": "tweet", "id": "1923302682590085254", "text": "@LucaBizzarri @ottobrerosa Ma rispondi nel merito, cosa c’entrano le “passioni personali”? È questo il rispetto per le donne? \nE non venirmi a parlare di democrazia proprio tu che hai sostenuto l’insostenibile (ti potrei fare una lista di tutti i voli pindarici per appoggiare atti antidemocratici).", "author_id": "554447353", "author_username": "w<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 4, "like_count": 22, "quote_count": 0, "view_count": 1064}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923349581812703646", "timestamp": "2025-05-16T12:07:29+00:00", "timestamp_unix": 1747397249, "type": "comment", "text": "@wnamass @ottobrerosa “È sempre comunque giusto che chiunque si possa esprimere su tutto” \n\"E tergicelo sarai tu\"\nChe poi è quello che ho scritto.", "context": {"type": "tweet", "id": "1923311117599723612", "text": "@LucaBizzarri @ottobrerosa Ad esempio rispondendo “È sempre comunque giusto che chiunque si possa esprimere su tutto”.\nMa non penso proprio tu riesca a dire una cosa del genere, è vero Luchino?", "author_id": "554447353", "author_username": "w<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 6, "quote_count": 0, "view_count": 471}}, {"conversation_id": "1923785270383518152", "tweet_id": "1923797805417554215", "timestamp": "2025-05-17T17:48:33+00:00", "timestamp_unix": 1747504113, "type": "comment", "text": "@madforfree Quel film uscì in streaming durante la pandemia, coi cinema chiusi, e io faccio l'attore, non il produttore. E cambierei i finanziamenti che certa sinistra ha fatto diventare una mangiatoia. Il fatto è che non state chiudendo la mangiatoia, state cambiando le vacche. Cordialità.", "context": {"type": "tweet", "id": "1924468051736420409", "text": "Bizzarrie...👇", "author_id": "1248931586491514883", "author_username": "PaguroBernardo2"}, "metrics": {"retweet_count": 61, "reply_count": 128, "like_count": 1664, "quote_count": 12, "view_count": 45797}}, {"conversation_id": "1923785270383518152", "tweet_id": "1924014414048395407", "timestamp": "2025-05-18T08:09:17+00:00", "timestamp_unix": 1747555757, "type": "comment", "text": "Alcuni furono rimandati, al<PERSON> (il nostro fu il primo) uscirono in streaming. Ho sempre detto quel che penso oggi ma dimentica una cosa: io faccio il comico. Lei fa la giornalista. <PERSON><PERSON> faccio ridere, lei dovrebbe informare. E visto quanto è informata lei, auguri ai suoi tanti lettori.", "context": {"type": "tweet", "id": "1924002199891251429", "text": "@LucaBizzarri Durante la pandemia tutti i film non furono trasmessi nei cinema, mica soltanto il suo. E se davvero il sistema non le stava bene come sostiene (ex post è facile) avremmo dovuto sentire la sua voce di protesta prima… ma non è accaduto 🤷🏻‍♀️", "author_id": "********", "author_username": "madfo<PERSON><PERSON>"}, "metrics": {"retweet_count": 1, "reply_count": 36, "like_count": 117, "quote_count": 1, "view_count": 8006}}, {"conversation_id": "1923768837318615416", "tweet_id": "1924038626280653257", "timestamp": "2025-05-18T09:45:30+00:00", "timestamp_unix": 1747561530, "type": "comment", "text": "@GiancarloDeRisi E li ho spesi tutti a disoneste.", "context": {"type": "tweet", "id": "192443*************", "text": "Togliere il finanziamento pubblico al cinema. Film,fiction all'italiana, sono solo Spazzatura.", "author_id": "1240749468619870208", "author_username": "Renzo3Renzo"}, "metrics": {"retweet_count": 17, "reply_count": 103, "like_count": 1484, "quote_count": 2, "view_count": 23038}}, {"conversation_id": "1923785270383518152", "tweet_id": "1924114978031784274", "timestamp": "2025-05-18T14:48:53+00:00", "timestamp_unix": **********, "type": "comment", "text": "@_SCALABRINO @madforfree \"Account satirico\". Non ti allargare.", "context": {"type": "tweet", "id": "1924113424180523117", "text": "@LucaBizzarri @madforfree \" Fai l'attore\". Non ti allargare.", "author_id": "*********", "author_username": "_SCALABRINO"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 385}}, {"conversation_id": "1923785270383518152", "tweet_id": "1924153884626341923", "timestamp": "2025-05-18T17:23:29+00:00", "timestamp_unix": **********, "type": "comment", "text": "@madforfree @Fabopolis Giornale che usufruisce, gius<PERSON><PERSON>, di contributi pubblici. Nel vostro caso meritatissimi, data l’autorevolezza della testata.", "context": {"type": "tweet", "id": "1923791827414462975", "text": "@Fabopolis @LucaBizzarri Premesso che non taggo mai nessuno se non il mio giornale, mi faccia divertire un po’: secondo lei non lo taggo perché mi intimorisce? 🤭 diversamente intelligenti e dove trovarvi", "author_id": "********", "author_username": "madfo<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 10, "quote_count": 0, "view_count": 337}}, {"conversation_id": "1923785270383518152", "tweet_id": "1924173127392563390", "timestamp": "2025-05-18T18:39:57+00:00", "timestamp_unix": 1747593597, "type": "comment", "text": "@madforfree L’importante è farlo consapevolmente. Auguri e occhio al 5g.", "context": {"type": "tweet", "id": "1924047276038668783", "text": "@LucaBizzarri Lei farà anche il comico ma fa più ridere quando parla di politica. Auguri a lei!", "author_id": "********", "author_username": "madfo<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 4, "like_count": 31, "quote_count": 0, "view_count": 1899}}, {"conversation_id": "1923785270383518152", "tweet_id": "1924173779518767166", "timestamp": "2025-05-18T18:42:33+00:00", "timestamp_unix": 1747593753, "type": "comment", "text": "@garrolucio1 @madforfree @Sk_u_l_l_y La cosa divertente è che in quel film c’è anche un attore (bravissimo e amico mio) che di secondo nome fa Benito. Ma se lo sono dimenticato.", "context": {"type": "tweet", "id": "1924170571815727593", "text": "@madforfree @Sk_u_l_l_y @LucaBizzarri Rispondi a Maximo, o preferisci nasconderti o fuggire ....", "author_id": "1064150133313609728", "author_username": "garrolucio1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 403}}]}