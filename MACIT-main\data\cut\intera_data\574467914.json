{"user_id": "574467914", "interactions_count": 18, "interactions": [{"conversation_id": "1912165269272748164", "tweet_id": "1912282104613724664", "timestamp": "2025-04-15T23:09:16+00:00", "timestamp_unix": 1744758556, "type": "comment", "text": "@FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @DentedNotBroken @krassenstein Lol...this is the same reasoning that made <PERSON> to start the tariff war with China which he's now clearly losing. Ur eyes will be clear very soon that China doesn't give a shit about ur so called consumer market u claim u have.", "context": {"type": "tweet", "id": "1912247810004775329", "text": "@brucebanner455 @Oppie4547 @grayphil27 @DentedNotBroken @krassenstein The US represents 26% of the entire world's GDP. The US also represents 30% of the world's wealth. China does not have any leverage in a trade war with the US given their primary customer is the U.S.", "author_id": "36563254", "author_username": "FilmLadd"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 59}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912412399790944737", "timestamp": "2025-04-16T07:47:01+00:00", "timestamp_unix": 1744789621, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein They're implementing retaliatory tariff to show <PERSON> that two can play the game.", "context": {"type": "tweet", "id": "1912360242001711418", "text": "If China is winning, why are they implementing higher retaliatory tariffs?\nIt’s almost like they are trying to back us down because they are scared that if the US stops buying their products and propping up their economy, it will decimate their economy.\n\nTo which many of us say, let the implosions begin.😂🤣🤣", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912450541080236268", "timestamp": "2025-04-16T10:18:35+00:00", "timestamp_unix": 1744798715, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein If China's economy cannot survive, so also that of the US. If the US is so superior as u all want to make it sound, u shouldn't have to depend on China for anything in the first place. The US owes the highest debt in the whole world.", "context": {"type": "tweet", "id": "1912449956746613183", "text": "@muenzak @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein So let the tariff war continue and see if their economy can survive.😂🤣", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912458905193525376", "timestamp": "2025-04-16T10:51:49+00:00", "timestamp_unix": 1744800709, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein Lol...some of the factories/companies are still in the US but too expensive for an average American. What's the govt going to do to make US made goods affordable for an average American? Besides, how long do u think it will take a company to move production back to the US?", "context": {"type": "tweet", "id": "1912457892516581782", "text": "@muenzak @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein Exactly, we shouldn’t have to depend on China. So bring manufacturing back and let China figure out how to survive without their biggest customer.", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912461682242105368", "timestamp": "2025-04-16T11:02:51+00:00", "timestamp_unix": 1744801371, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein It's easier to boast on X but the reality says otherwise. Anyway, hopefully we shall all be alive to see how all this pans out eventually.", "context": {"type": "tweet", "id": "1912460837853261902", "text": "Oh well. Short term pain for long term gain. Plus we can do without cheap Chinese crap. Not to mention, entrepreneurs in the US will quickly step up and find a way to quickly take advantage of the vacancy of products in the market.\n\nAnd on a side note, fighting to keep the status quo is a position of weakness.", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912463283153490093", "timestamp": "2025-04-16T11:09:13+00:00", "timestamp_unix": 1744801753, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein Lol...what fear? My country is not in any tariff war with China. My home country Nigeria will definitely choose China over the US any time any day becos I can see clearly some of the capital projects China has done for my country but can't say anything abt the US. So, what fear?", "context": {"type": "tweet", "id": "1912462127295520967", "text": "@muenzak @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein Hopefully be alive?😂🤣😂\n\nEvery post you make screams of fear.\n\nIt’s a syndrome of weak men.", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 40}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912468248055988379", "timestamp": "2025-04-16T11:28:56+00:00", "timestamp_unix": 1744802936, "type": "comment", "text": "@DentedNotBroken @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein U can blame the X algorithm but again, I decided to engage to curtail the arrogance of some Americans like u who think the world cannot and shouldn't function without the US. China has shown u they don't care, yet u keep pontificating about how their economy will crumble 🤣🤣🤣", "context": {"type": "tweet", "id": "1912467305545544087", "text": "@muenzak @FilmLadd @brucebanner455 @Oppie4547 @grayphil27 @krassenstein So why are you even in this thread then if it doesn’t affect you? 😂🤣🤣", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912473420681326721", "timestamp": "2025-04-16T11:49:30+00:00", "timestamp_unix": 1744804170, "type": "comment", "text": "@Oppie4547 @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein Lol...I can't remember buying anything made in the US and I'm still alive. If anything, China has shown u where u belong and to wake u up from ur own fake bubbles of importance. Enough said but I know Trumpsters always pretend until the reality hits them in the face. 🤣🤣🤣", "context": {"type": "tweet", "id": "1912472642503713042", "text": "@muenzak @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein The world can't function without the United States. That's why all of you bloodsuckers, have your hands out.", "author_id": "1863635380668432384", "author_username": "Oppie4547"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912475166333870183", "timestamp": "2025-04-16T11:56:26+00:00", "timestamp_unix": 1744804586, "type": "comment", "text": "@Oppie4547 @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein hahaha...if u're not poor, why have u all resorted to buying cheap items from China? 🤣🤣🤣", "context": {"type": "tweet", "id": "1912474795121275089", "text": "@muenzak @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein The fact that you're poor isn't relevant.", "author_id": "1863635380668432384", "author_username": "Oppie4547"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912477630525264172", "timestamp": "2025-04-16T12:06:13+00:00", "timestamp_unix": 1744805173, "type": "comment", "text": "@DentedNotBroken @Oppie4547 @FilmLadd @brucebanner455 @grayphil27 @krassenstein U can still make ur stuff in ur country without resorting to fighting dirty, without being disrespectful like <PERSON><PERSON> calling the Chinese peasants and without being cowardly arrogant like <PERSON> saying countries are kissing his arse to make a deal. China doesn't make noise. 🤣🤣", "context": {"type": "tweet", "id": "1912476843451556314", "text": "@muenzak @Oppie4547 @FilmLadd @brucebanner455 @grayphil27 @krassenstein Why are you fighting to keep people buying cheap China crap?\n\nOh wait, it’s so you can have them make capital improvements to your country because your country is too poor to do it themselves.😂🤣", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 37}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912479007976698192", "timestamp": "2025-04-16T12:11:42+00:00", "timestamp_unix": 1744805502, "type": "comment", "text": "@DentedNotBroken @Oppie4547 @FilmLadd @brucebanner455 @grayphil27 @krassenstein hahaha...ur leaders including <PERSON> make u all Chinese slaves by making u dependent on China for ur essentials. China doesn't need me to fight for them but their hardwork makes the noise, unlike the Americans who think they simply have the right to dictate the terms. 🤣🤣", "context": {"type": "tweet", "id": "1912477984180613581", "text": "@muenzak @Oppie4547 @FilmLadd @brucebanner455 @grayphil27 @krassenstein Look at you fighting for your Chinese masters.\n\n“Enslave me harder daddy” 😂🤣😂", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912479725793984805", "timestamp": "2025-04-16T12:14:33+00:00", "timestamp_unix": 1744805673, "type": "comment", "text": "@grayphil27 @DentedNotBroken @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Why do u expect Trumpsters not to miss the point? It's the same way <PERSON> and his circus missed the point and started the tariff wars which they're now clearly losing to China. He thinks they can just bully anyone into submission. 🤣🤣🤣", "context": {"type": "tweet", "id": "1912478924048564344", "text": "@DentedNotBroken @m<PERSON>zak @Oppie4547 @FilmLadd @brucebanner455 @krassenstein I really think you have missed the point here.", "author_id": "2231213407", "author_username": "grayphil27"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912482796099993879", "timestamp": "2025-04-16T12:26:45+00:00", "timestamp_unix": 1744806405, "type": "comment", "text": "@Oppie4547 @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein hahaha...if it's so easy as u all have been brainwashed to believe, u can just simply do it without making so much noise but <PERSON> and his fellow clowns know the truth. That's why they've resorted to all this tariff noise up and down🤣🤣🤣", "context": {"type": "tweet", "id": "1912481476521742391", "text": "@muenzak @DentedNotBroken @FilmLadd @brucebanner455 @grayphil27 @krassenstein Apparently, what you don't understand is we're bringing manufacturing back to America, so we don't have to buy chinese products anymore.\nWhy are you so confused on this basic fact?", "author_id": "1863635380668432384", "author_username": "Oppie4547"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912490960350494889", "timestamp": "2025-04-16T12:59:11+00:00", "timestamp_unix": 1744808351, "type": "comment", "text": "@grayphil27 @Oppie4547 @DentedNotBroken @FilmLadd @brucebanner455 @krassenstein 🤣🤣🤣🤣🤣", "context": {"type": "tweet", "id": "1912483142507639210", "text": "@Oppie4547 @m<PERSON>zak @DentedNotBroken @FilmLadd @brucebanner455 @krassenstein Hahaha and then the funniest thing, i clicked on your profile, and you are drinking at home\n\nBe honest, you couldn't walk into a bar, and say \"this rounds on me\" \n\nCos you are poor..", "author_id": "2231213407", "author_username": "grayphil27"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912496675328864426", "timestamp": "2025-04-16T13:21:54+00:00", "timestamp_unix": 1744809714, "type": "comment", "text": "@DentedNotBroken @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein hahaha...I'm simping for Chinese masters but u're still buying their products. Instead of slapping tariffs on Chinese products, why not simply openly ban the Chinese products from being imported into the US? 🤣🤣🤣", "context": {"type": "tweet", "id": "1912496065187619086", "text": "@grayphil27 @m<PERSON><PERSON> @Oppie4547 @FilmLadd @brucebanner455 @krassenstein What point? That the dude is simping for his Chinese masters because they are propping up his country?\n\nAnd now your white knighting for him?", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912497448045420722", "timestamp": "2025-04-16T13:24:58+00:00", "timestamp_unix": 1744809898, "type": "comment", "text": "@DentedNotBroken @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Yea...just do it 🤣🤣🤣", "context": {"type": "tweet", "id": "1912497241559855583", "text": "@muenzak @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Great idea. I’ve been saying we should ban them for awhile. China will fall even faster.😂", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912498897697624074", "timestamp": "2025-04-16T13:30:44+00:00", "timestamp_unix": 1744810244, "type": "comment", "text": "@DentedNotBroken @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein I speak on fact and reality, not some fantasies but yea...u're free to dwell on ur fantasies. I would rather have my leader show results than just being noisy, arrogant, and disrespectful to any nation just to make any silly point.", "context": {"type": "tweet", "id": "1912498046211932574", "text": "@muenzak @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Wish I had the power too. \n\nWhat will you do when China can’t prop up your country? Go back to grifting old ladies out of their retirement money as a Nigerian Prince looking for love?🤡😂", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1912165269272748164", "tweet_id": "1912501093222805787", "timestamp": "2025-04-16T13:39:27+00:00", "timestamp_unix": 1744810767, "type": "comment", "text": "@DentedNotBroken @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Lol...ur question should be directed to ur president, <PERSON>, who is an excellent example of someone who lacks fatherly love while growing up and whose ego is easily hurt. I will be ashamed if my president openly says other world leaders are kissing his arse. Classless 🤡🤣", "context": {"type": "tweet", "id": "1912500251878948896", "text": "@muenzak @grayphil27 @Oppie4547 @FilmLadd @brucebanner455 @krassenstein Noisy, arrogant and disrespectful are words women use when their fragile feelings are hurt and offended.\n\nIs it a heavy soy diet or no father in the house when growing up, cupcake?", "author_id": "*********", "author_username": "DentedNotBroken"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}]}