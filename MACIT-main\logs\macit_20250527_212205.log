2025-05-27 21:22:05,396 - test_10_users - INFO - 开始测试10个用户的意图分析...
2025-05-27 21:22:08,180 - test_10_users - INFO - MACIT框架初始化成功
2025-05-27 21:22:08,183 - test_10_users - INFO - 将测试以下10个用户: ['1004599646080131072', '1021702922927669249', '1027405890314358784', '1034308064', '1037663718686031879', '1038128766575202306', '1058187164', '1064220744677232642', '1070439999517024257', '1070616509423280128']
2025-05-27 21:22:08,184 - test_10_users - INFO - 正在分析用户 1004599646080131072 (1/10)...
2025-05-27 21:26:34,632 - test_10_users - INFO - 用户 1004599646080131072 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1004599646080131072\interaction_0.json
2025-05-27 21:26:34,634 - test_10_users - INFO - 正在分析用户 1021702922927669249 (2/10)...
2025-05-27 21:30:58,563 - test_10_users - INFO - 用户 1021702922927669249 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1021702922927669249\interaction_0.json
2025-05-27 21:30:58,563 - test_10_users - INFO - 正在分析用户 1027405890314358784 (3/10)...
2025-05-27 21:35:52,986 - test_10_users - INFO - 用户 1027405890314358784 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1027405890314358784\interaction_0.json
2025-05-27 21:35:52,987 - test_10_users - INFO - 正在分析用户 1034308064 (4/10)...
2025-05-27 21:40:43,229 - test_10_users - INFO - 用户 1034308064 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1034308064\interaction_0.json
2025-05-27 21:40:43,230 - test_10_users - INFO - 正在分析用户 1037663718686031879 (5/10)...
2025-05-27 21:46:11,332 - test_10_users - INFO - 用户 1037663718686031879 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1037663718686031879\interaction_0.json
2025-05-27 21:46:11,333 - test_10_users - INFO - 正在分析用户 1038128766575202306 (6/10)...
2025-05-27 21:51:29,303 - test_10_users - INFO - 用户 1038128766575202306 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1038128766575202306\interaction_0.json
2025-05-27 21:51:29,304 - test_10_users - INFO - 正在分析用户 1058187164 (7/10)...
2025-05-27 21:51:50,655 - test_10_users - ERROR - 分析用户 1058187164 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 400 - {'error': {'code': 'data_inspection_failed', 'param': None, 'message': 'Output data may contain inappropriate content.', 'type': 'data_inspection_failed'}, 'id': 'chatcmpl-9fb2dc62-af1b-90ee-9eea-5e55a5e48f4d', 'request_id': '9fb2dc62-af1b-90ee-9eea-5e55a5e48f4d'}
2025-05-27 21:51:50,655 - test_10_users - INFO - 正在分析用户 1064220744677232642 (8/10)...
2025-05-27 21:58:23,508 - test_10_users - INFO - 用户 1064220744677232642 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1064220744677232642\interaction_0.json
2025-05-27 21:58:23,509 - test_10_users - INFO - 正在分析用户 1070439999517024257 (9/10)...
2025-05-27 22:04:29,463 - test_10_users - INFO - 用户 1070439999517024257 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1070439999517024257\interaction_0.json
2025-05-27 22:04:29,463 - test_10_users - INFO - 正在分析用户 1070616509423280128 (10/10)...
2025-05-27 22:10:37,481 - test_10_users - INFO - 用户 1070616509423280128 分析完成，结果保存至: D:\PhDJHLiu\MACIT-main\MACIT-main\output\1070616509423280128\interaction_0.json
2025-05-27 22:10:37,482 - test_10_users - INFO - 所有用户分析完成，正在整理结果...
2025-05-27 22:10:37,483 - test_10_users - INFO - 格式化结果已保存至: output\test_10_users_results.txt
