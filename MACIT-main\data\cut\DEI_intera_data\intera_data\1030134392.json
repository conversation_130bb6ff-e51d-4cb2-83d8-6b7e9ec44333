{"user_id": "1030134392", "interactions_count": 15, "interactions": [{"conversation_id": "1920155683598503991", "tweet_id": "1920396700251803662", "timestamp": "2025-05-08T08:33:47+00:00", "timestamp_unix": 1746693227, "type": "comment", "text": "@Supersonico1899 @SkySport Che bello vedere voi milanisti rosicare come nutrie. Ma vi capisco in fondo.🦫", "context": {"type": "tweet", "id": "1920837521325375828", "text": "Cronisti vergognosamente tifosi. Uno schifo.", "author_id": "1361388554501423106", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 130}}, {"conversation_id": "1920155683598503991", "tweet_id": "1920422551290818728", "timestamp": "2025-05-08T10:16:30+00:00", "timestamp_unix": 1746699390, "type": "comment", "text": "@basten_van42090 @Supersonico1899 @SkySport Scritto da uno che si chiama <PERSON> 🤣🤣🤣🤣🤣🤣🦫🦫🦫🦫", "context": {"type": "tweet", "id": "1920421525074641040", "text": "@CatoneEnrico @Supersonico1899 @SkySport Non c'entra. Onore all'inter, ma <PERSON><PERSON> e <PERSON>ssa non si potevano ascoltare", "author_id": "1826036599945072640", "author_username": "basten_van42090"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1920418907279573384", "tweet_id": "1920479699177709713", "timestamp": "2025-05-08T14:03:35+00:00", "timestamp_unix": 1746713015, "type": "comment", "text": "@dscndt @marattin @Partito_Libdem Beh su quello ognuno si faccia la sua opinione.", "context": {"type": "tweet", "id": "1920472003804356922", "text": "@marattin @Partito_Libdem E sul referendum relativo alla cittadinanza?", "author_id": "490730485", "author_username": "dscndt"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 231}}, {"conversation_id": "1920418907279573384", "tweet_id": "1920479826407817660", "timestamp": "2025-05-08T14:04:06+00:00", "timestamp_unix": 1746713046, "type": "comment", "text": "@cicciput68 @marat<PERSON> @Partito_Libdem Magari ne avessimo uno.", "context": {"type": "tweet", "id": "1920471653923869152", "text": "@marattin @Partito_Libdem Sesto viva Milei! Immagino 😉", "author_id": "830924168", "author_username": "cicciput68"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1920758988414595453", "tweet_id": "1920762624075059264", "timestamp": "2025-05-09T08:47:50+00:00", "timestamp_unix": 1746780470, "type": "comment", "text": "@Foscari1991 È la parata della vittoria.... Cinese", "context": {"type": "tweet", "id": "1921288155828789534", "text": "I tempi sono cambiati.", "author_id": "1755588984271421440", "author_username": "IZopp82912"}, "metrics": {"retweet_count": 2, "reply_count": 0, "like_count": 17, "quote_count": 0, "view_count": 729}}, {"conversation_id": "1920701903916191821", "tweet_id": "1920764257378927055", "timestamp": "2025-05-09T08:54:19+00:00", "timestamp_unix": 1746780859, "type": "comment", "text": "@Edoenonsolo In che zona abiti?", "context": {"type": "tweet", "id": "1920701903916191821", "text": "原始推文内容不可用", "author_id": "326854012", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 44}}, {"conversation_id": "1920701903916191821", "tweet_id": "1920784027746705412", "timestamp": "2025-05-09T10:12:53+00:00", "timestamp_unix": 1746785573, "type": "comment", "text": "@Edoenonsolo Ma dai. Mai visti finora", "context": {"type": "tweet", "id": "1920781122402718022", "text": "@CatoneEnrico In Veneto.", "author_id": "326854012", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921086261747478723", "tweet_id": "1921110720487325799", "timestamp": "2025-05-10T07:51:02+00:00", "timestamp_unix": 1746863462, "type": "comment", "text": "@colvieux Che ci siano stati stupri e crimini da parte dell' armata rossa è storicamente provato. Non esiste alcuna discussione.", "context": {"type": "tweet", "id": "1921474501809693035", "text": "Non posso risponderti, ma un applauso te lo faccio lo stesso 👏🏻 \n(e aggiungo un follow)", "author_id": "86716636", "author_username": "rob<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 7, "quote_count": 0, "view_count": 163}}, {"conversation_id": "1921086261747478723", "tweet_id": "1921111041917759490", "timestamp": "2025-05-10T07:52:19+00:00", "timestamp_unix": 1746863539, "type": "comment", "text": "@colvieux Sono gli stessi che negano gli stupri avvenuti il 7 ottobre ovviamente", "context": {"type": "tweet", "id": "1921090403140325589", "text": "<PERSON>.s se si è in buona fede, si apre il link e si verifica. Ma nel tribalismo ideologico è come con i no vax: si nega, si insulta e non si verifica.", "author_id": "444225252", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1921086261747478723", "tweet_id": "1921111475566895450", "timestamp": "2025-05-10T07:54:02+00:00", "timestamp_unix": 1746863642, "type": "comment", "text": "@domeralo @colvieux La memoria dell' Unione sovietica è già ampiamente infangata da tutti i crimini commessi ne 70 anni della sua esistenza", "context": {"type": "tweet", "id": "1921103753093976490", "text": "@colvieux Post vergognoso che infanga la memoria dello sforzo sovietico contro la belva nazista e della sua vittoria. Una criminalizzazione condotta con l'intento apparente dell'omaggio alla storiografia di genere... Mi chiedo perché la stessa autrice abbia taicuto gli stupri nazisti ...", "author_id": "1497230051628261390", "author_username": "domeralo"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1921233541934756025", "tweet_id": "1921502472922665109", "timestamp": "2025-05-11T09:47:43+00:00", "timestamp_unix": 1746956863, "type": "comment", "text": "@giovamartinelli Purtroppo la TV crea mostri", "context": {"type": "tweet", "id": "1922218068446478483", "text": "<PERSON><PERSON><PERSON> di chi? Di poveri ingenui, allocchi che si bevono la propaganda e la disinformazione, ammirandone con tutto il cuore l’artefice che fa i video dalla sua macchina", "author_id": "2429648445", "author_username": "marcoorioles"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 4, "quote_count": 0, "view_count": 118}}, {"conversation_id": "1922338473731580017", "tweet_id": "1922557623271919765", "timestamp": "2025-05-14T07:40:31+00:00", "timestamp_unix": 1747208431, "type": "comment", "text": "@rulajebreal Sembra una bufala, hai verificato la notizia?", "context": {"type": "tweet", "id": "1924232919767282077", "text": "Israele = la vergogna dell’umanità!! Mi auguro che il governò Netanyhau e chi lo sostiene sprofondi all’inferno quanto prima !! Verr<PERSON> il giorno in cui pagheranno per tutto questo", "author_id": "1153800518973755392", "author_username": "cavallo19273904"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 287}}, {"conversation_id": "1922338473731580017", "tweet_id": "1922557884820291828", "timestamp": "2025-05-14T07:41:33+00:00", "timestamp_unix": 1747208493, "type": "comment", "text": "@L_Apostata @guffanti_marco @rulajebreal Io mi aspetto che una giornalista verifichi le notizie.", "context": {"type": "tweet", "id": "1922439970981097634", "text": "@guffanti_marco @rulajebreal Tu davvero aspetti davvero che si presenti un portavoce dell'IDF a dire \"sì, confermo, abbiamo ucciso noi il dodicenne testimone di un crimine di guerra\"?\nSiete la versione più scema di quelli della cocaina di Macron.", "author_id": "4259043179", "author_username": "L_Apostata"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1922364006246863055", "tweet_id": "1922725890405892374", "timestamp": "2025-05-14T18:49:09+00:00", "timestamp_unix": 1747248549, "type": "comment", "text": "@AmedeoMad<PERSON> @Parabel66836534 La gente crede a quello che vuole credere", "context": {"type": "tweet", "id": "1922559706469482509", "text": "@Parabel66836534 Sì ma davvero, ma che noia... ma come si fa a credere a ste robe...", "author_id": "1461268131918270464", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1923015429112349123", "tweet_id": "1923291525871538392", "timestamp": "2025-05-16T08:16:47+00:00", "timestamp_unix": 1747383407, "type": "comment", "text": "@nonceduesenza3 @LucaBizzarri Oddio c'è comunque il vaglio di costituzionalità sui quesiti. Io proporrei di aumentare il numero di firme necessarie per la presentazione dei quesiti ed abbasserei il quorum.", "context": {"type": "tweet", "id": "1923025701466218801", "text": "@LucaBizzarri Il quorum non è una regola stupida,serve ad evitare che il primo saltimbanco che passa ci trascini alle urne ogni cazzo di domenica per stravolgere leggi scritte dai nostri rappresentanti al Parlamento\nI masanielli che sanno cos'è bene per il popolo non finiscono mai, ricordatelo", "author_id": "1794009769998508033", "author_username": "nonceduesenza3"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 39}}]}