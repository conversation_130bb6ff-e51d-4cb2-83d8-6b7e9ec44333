{"user_id": "1272570747039428609", "interactions_count": 15, "interactions": [{"conversation_id": "1919794455617442167", "tweet_id": "1920008073491411388", "timestamp": "2025-05-07T06:49:31+00:00", "timestamp_unix": 1746600571, "type": "comment", "text": "@aa4323801448919 🤮", "context": {"type": "tweet", "id": "1920216462892958075", "text": "@PainAvalanche Beh mai quanto sotto il mio sul tavernello nei bicchieri di plastica...\nMa LOL...\nhttps://t.co/TLL7Mcb6U7", "author_id": "1713983129810083840", "author_username": "aa4323801448919"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 48}}, {"conversation_id": "1920050076828737835", "tweet_id": "1920056825547751737", "timestamp": "2025-05-07T10:03:14+00:00", "timestamp_unix": 1746612194, "type": "comment", "text": "@partigggiano Vado controtendenza, invito gli antisemiti mascherati da antisionisti, a mettersi quell’adesivo in fronte, quantomeno i titolari degli esercizi pubblici simpatizzanti eviteranno di perdere tempo a origliare le loro conversazioni ai tavoli.", "context": {"type": "tweet", "id": "1920473946509418873", "text": "Ci avevano già provato con metodo scientifico e approccio sartoriale 😢", "author_id": "956986276988751872", "author_username": "givalietti5409"}, "metrics": {"retweet_count": 8, "reply_count": 5, "like_count": 103, "quote_count": 1, "view_count": 1591}}, {"conversation_id": "1920050076828737835", "tweet_id": "1920056936252256746", "timestamp": "2025-05-07T10:03:41+00:00", "timestamp_unix": 1746612221, "type": "comment", "text": "@GufoPuso @partigggiano Genio!", "context": {"type": "tweet", "id": "1920055108194226510", "text": "@partigggiano Suvvia, siamo nel XXI secolo:  \nun'app obbligatoria sul telefono e un comodo scanner all'ingresso, con voce robotica che fa \"JEW!JEW!JEW!\" se un sionista ci passa sotto...", "author_id": "1658584187580940289", "author_username": "<PERSON><PERSON>o<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 33}}, {"conversation_id": "1920050076828737835", "tweet_id": "1920057134529532196", "timestamp": "2025-05-07T10:04:28+00:00", "timestamp_unix": 1746612268, "type": "comment", "text": "@agnosticIT @partigggiano Mmmmhh già usato il tatuaggio", "context": {"type": "tweet", "id": "1920055591487090974", "text": "@partigggiano Un tatuaggio QR code sarebbe perfetto", "author_id": "223748747", "author_username": "agnosticIT"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1920050076828737835", "tweet_id": "1920064163482616316", "timestamp": "2025-05-07T10:32:24+00:00", "timestamp_unix": 1746613944, "type": "comment", "text": "@partigggiano Dici che si sentirebbero discriminati?", "context": {"type": "tweet", "id": "1920057360946434420", "text": "@Cris_quella Non vorrei arrecargli troppo disturbo", "author_id": "1500454901620645892", "author_username": "<PERSON>ig<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 15, "quote_count": 0, "view_count": 231}}, {"conversation_id": "1920050076828737835", "tweet_id": "1920098029798826385", "timestamp": "2025-05-07T12:46:58+00:00", "timestamp_unix": 1746622018, "type": "comment", "text": "@ValeryMell1 @partigggiano Grazie☺️", "context": {"type": "tweet", "id": "1920080576423293107", "text": "@Cris_quella @partig<PERSON><PERSON>h<PERSON>h, meravigliosi entrambi 👏👏", "author_id": "585455321", "author_username": "ValeryMell1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 6, "quote_count": 0, "view_count": 139}}, {"conversation_id": "1920701903916191821", "tweet_id": "1920731526615937463", "timestamp": "2025-05-09T06:44:16+00:00", "timestamp_unix": 1746773056, "type": "comment", "text": "@Edoenonsolo Che carino!", "context": {"type": "tweet", "id": "1920701903916191821", "text": "原始推文内容不可用", "author_id": "326854012", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1921111982922473949", "tweet_id": "1921166813439606824", "timestamp": "2025-05-10T11:33:56+00:00", "timestamp_unix": 1746876836, "type": "comment", "text": "@salvfor3 @marsetac É stato un regalo di Hamas, per ringraziatelo dell’aiuto fornito.", "context": {"type": "tweet", "id": "1921126271733338450", "text": "@marsetac @Cris_quella Proprio per ricordare che lui è nato per portare terrore e morte 7 Ottobre compleanno di #PutinIsaWarCriminal https://t.co/H3LGEmyR7c", "author_id": "2569507486", "author_username": "salvfor3"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1921241080650957258", "tweet_id": "1921259618069598274", "timestamp": "2025-05-10T17:42:42+00:00", "timestamp_unix": 1746898962, "type": "comment", "text": "@_Brick_Block_ Se solo la Albanese smettesse di scrivere, e dire, tante stronzate! 🤮", "context": {"type": "tweet", "id": "1921460919671898524", "text": "Caspita che tribunale grande ci vorrà per processare tutti i complici. Spero si cominci dai bambini.", "author_id": "2429648445", "author_username": "marcoorioles"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 8, "quote_count": 0, "view_count": 110}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921259856117284975", "timestamp": "2025-05-10T17:43:39+00:00", "timestamp_unix": 1746899019, "type": "comment", "text": "@smilingPamPam Standing ovation 😂", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 267}}, {"conversation_id": "1921480965496426821", "tweet_id": "1921639711908446554", "timestamp": "2025-05-11T18:53:04+00:00", "timestamp_unix": 1746989584, "type": "comment", "text": "@Milton_Keynes1 @LucaMancus @Axen0s Non ci posso credere 😂😂😂😂😂😂😂😂", "context": {"type": "tweet", "id": "1921615604521324874", "text": "@LucaMancus @Axen0s Qui quando confonde i litri con l’alcolemia nel sangue. https://t.co/e7ZimrG4Sx", "author_id": "1086684922100756480", "author_username": "Milton_Keynes1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921812774251856080", "tweet_id": "1921957570601742402", "timestamp": "2025-05-12T15:56:07+00:00", "timestamp_unix": 1747065367, "type": "comment", "text": "@annalisa_pis <PERSON>, non sei genitore, limitati a giudicare quello che ti compete.", "context": {"type": "tweet", "id": "1922876807029600623", "text": "Eh la madonna. \nMa questa quante volte è caduta dal seggiolone?", "author_id": "1643232008464654338", "author_username": "AleDAmicocats"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 5, "quote_count": 0, "view_count": 118}}, {"conversation_id": "1921812774251856080", "tweet_id": "1921958933498892560", "timestamp": "2025-05-12T16:01:32+00:00", "timestamp_unix": 1747065692, "type": "comment", "text": "@annalisa_pis @Emme_comegesooo Te lo spiego facile, anche se non era difficile capirlo: mi piace vederlo ogni volta che si accende lo schermo.", "context": {"type": "tweet", "id": "1921889406270492937", "text": "@Emme_comegesooo Ma io anche se ne avessi non utilizzerei le loro foto in questa maniera. Qual è lo scopo di questa azione? Il bisogno sotteso? È l'equivalente della foto nel portafogli? Intanto nessuno mi spiega la ratio, al più hanno risposto \"boh, perché sì\".", "author_id": "1172868917095804929", "author_username": "annalisa_pis"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 95}}, {"conversation_id": "1922334015903699066", "tweet_id": "1922365738666303847", "timestamp": "2025-05-13T18:58:02+00:00", "timestamp_unix": 1747162682, "type": "comment", "text": "@SammLilith @allegronntroppo É un fake o un troll, segnalare e bloccare!", "context": {"type": "tweet", "id": "1922363848478109879", "text": "Gente orribile e come riconoscerla...ed isolarla", "author_id": "1052894605", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 37}}, {"conversation_id": "1922959975606820953", "tweet_id": "1923087374591873376", "timestamp": "2025-05-15T18:45:33+00:00", "timestamp_unix": 1747334733, "type": "comment", "text": "@FranAltomare Oddio che orrore!", "context": {"type": "tweet", "id": "1923025416375202016", "text": "Non x essere razzista ma questa cosa non potrebbe mai succedere al nord🤣", "author_id": "1547565807097368578", "author_username": "francigua74"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}]}