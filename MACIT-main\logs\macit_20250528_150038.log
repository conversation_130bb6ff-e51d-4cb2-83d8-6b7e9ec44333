2025-05-28 15:00:42,868 - run_unprocessed - INFO - 开始运行所有未处理的用户数据
2025-05-28 15:00:42,882 - run_unprocessed - INFO - 总用户数: 500
2025-05-28 15:00:42,883 - run_unprocessed - INFO - 已处理用户数: 57
2025-05-28 15:00:42,883 - run_unprocessed - INFO - 未处理用户数: 443
2025-05-28 15:00:42,883 - run_unprocessed - INFO - 正在初始化MACIT框架（使用DeepSeek-chat模型）...
2025-05-28 15:00:42,903 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-28 15:00:42,903 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-28 15:00:42,904 - data_utils - INFO - 成功加载话题背景信息
2025-05-28 15:00:42,904 - data_utils - INFO - 成功加载话题背景信息
2025-05-28 15:00:42,905 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:42,905 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:42,905 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:42,905 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:43,498 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:43,498 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,344 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-28 15:00:44,344 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-28 15:00:44,345 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,345 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,347 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:44,347 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:44,867 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,867 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,868 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-28 15:00:44,868 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-28 15:00:44,868 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,868 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:44,869 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:44,869 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:45,383 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,383 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,383 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-28 15:00:45,383 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-28 15:00:45,384 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,384 - model_interface - INFO - 使用DeepSeek平台创建模型，名称: deepseek-chat, URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,384 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:45,384 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-28 15:00:45,850 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,850 - model_interface - INFO - 成功创建自定义模型: deepseek-chat 使用DeepSeek平台，URL: https://api.deepseek.com/v1
2025-05-28 15:00:45,851 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-28 15:00:45,851 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-28 15:00:45,852 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-28 15:00:45,852 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-28 15:00:45,852 - run_unprocessed - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-28 15:00:45,853 - run_unprocessed - INFO - 将要分析 443 个未处理用户的细粒度结构化意图
2025-05-28 15:00:45,853 - run_unprocessed - INFO - 正在分析用户 1058187164 (1/443)...
2025-05-28 15:00:45,854 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-28 15:00:45,854 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-28 15:00:45,855 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-28 15:00:45,855 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-28 15:00:45,855 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:45,855 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:45,855 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:45,855 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:45,856 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-28 15:00:45,856 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-28 15:00:45,856 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-28 15:00:45,856 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-28 15:00:45,857 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-28 15:00:45,857 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-28 15:00:45,857 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-28 15:00:45,857 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-28 15:00:45,857 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-28 15:00:45,857 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-28 15:00:46,471 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-28 15:00:46,472 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x00000279A0431600>
2025-05-28 15:00:46,472 - camel.agents.chat_agent - ERROR - An error occurred while running model deepseek-chat, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'error': {'message': 'Content Exists Risk', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_request_error'}}
2025-05-28 15:00:46,577 - macit_framework - ERROR - 上下文分析触发内容风险检查
2025-05-28 15:00:46,577 - macit_framework - ERROR - 上下文分析触发内容风险检查
2025-05-28 15:00:46,577 - run_unprocessed - WARNING - 跳过用户 1058187164: 上下文分析内容风险
2025-05-28 15:00:47,585 - run_unprocessed - INFO - 正在分析用户 1322486724178505728 (2/443)...
2025-05-28 15:00:47,586 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-28 15:00:47,586 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-28 15:00:47,587 - data_utils - INFO - 成功加载用户 1322486724178505728 的交互数据，共 29 条交互
2025-05-28 15:00:47,587 - data_utils - INFO - 成功加载用户 1322486724178505728 的交互数据，共 29 条交互
2025-05-28 15:00:47,587 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:47,587 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:47,587 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:47,587 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-28 15:00:47,588 - data_utils - INFO - 成功加载用户 1322486724178505728 的交互数据，共 29 条交互
2025-05-28 15:00:47,588 - data_utils - INFO - 成功加载用户 1322486724178505728 的交互数据，共 29 条交互
2025-05-28 15:00:47,588 - data_utils - WARNING - 未找到用户 1322486724178505728 的画像
2025-05-28 15:00:47,588 - data_utils - WARNING - 未找到用户 1322486724178505728 的画像
2025-05-28 15:00:47,588 - macit_framework - WARNING - 未找到用户 1322486724178505728 的画像，使用空画像
2025-05-28 15:00:47,588 - macit_framework - WARNING - 未找到用户 1322486724178505728 的画像，使用空画像
2025-05-28 15:00:47,589 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-28 15:00:47,589 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-28 15:00:47,589 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-28 15:00:47,589 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-28 15:00:47,653 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/v1/chat/completions "HTTP/1.1 200 OK"
