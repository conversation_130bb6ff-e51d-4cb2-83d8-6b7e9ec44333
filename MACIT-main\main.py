#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
MACIT (Multi-Agent Collaborative Intent Tagging) 框架主程序
用于启动框架并分析用户意图
"""

import argparse
import logging
import sys
import time
from pathlib import Path

from logger_config import setup_logger
from macit_framework import MACITFramework
from data_utils import get_all_user_ids

# 设置日志
logger = setup_logger('main')


def parse_args():
    """
    解析命令行参数

    Returns:
        解析后的参数
    """
    parser = argparse.ArgumentParser(description='MACIT框架 - 多智能体协作的意图标注')

    parser.add_argument(
        '--user_id',
        type=str,
        default=None,
        help='要分析的用户ID，如果不指定则分析所有用户',
    )

    parser.add_argument(
        '--interaction_index',
        type=int,
        default=0,
        help='要分析的交互索引，默认为0',
    )

    parser.add_argument(
        '--max_rounds',
        type=int,
        default=None,
        help='最大辩论轮数，如果不指定则使用配置文件中的值',
    )

    parser.add_argument(
        '--max_users',
        type=int,
        default=None,
        help='最大分析用户数量，如果不指定则分析所有用户',
    )

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 创建MACIT框架
    logger.info("正在初始化MACIT框架（细粒度结构化意图标签模型）...")
    framework = MACITFramework()
    logger.info("MACIT框架初始化完成，包含4个智能体")

    # 确定要分析的用户ID列表
    if args.user_id:
        user_ids = [args.user_id]
    else:
        # 获取所有可用的用户ID
        data_dir = Path("data/cut/intera_data")
        all_user_files = list(data_dir.glob("*.json"))
        user_ids = [f.stem for f in all_user_files]

        logger.info(f"发现 {len(user_ids)} 个用户数据文件")

        if args.max_users:
            user_ids = user_ids[:args.max_users]
            logger.info(f"限制处理前 {args.max_users} 个用户")

    logger.info(f"将要分析 {len(user_ids)} 个用户的细粒度结构化意图")

    # 分析每个用户的意图
    successful_analyses = 0
    failed_analyses = 0

    for i, user_id in enumerate(user_ids, 1):
        logger.info(f"正在分析用户 {user_id} ({i}/{len(user_ids)})...")

        try:
            # 分析用户意图
            result = framework.analyze_user_intent(
                user_id=user_id,
                interaction_index=args.interaction_index,
                max_rounds=args.max_rounds,
            )

            # 保存分析结果
            output_path = framework.save_analysis_result(
                result=result,
                user_id=user_id,
                interaction_index=args.interaction_index,
            )

            # 打印详细结果
            print(f"\n{'='*60}")
            print(f"用户 {user_id} 的细粒度结构化意图分析结果:")
            print(f"{'='*60}")

            # 上下文分析
            context_analysis = result.get('context_analysis', {})
            if context_analysis:
                print(f"📊 上下文分析:")
                analysis = context_analysis.get('analysis', {})
                if analysis:
                    core_topics = analysis.get('core_topics', {})
                    print(f"  - 核心话题: {core_topics.get('main_topic', 'N/A')}")
                    emotional_tendency = analysis.get('emotional_tendency', {})
                    print(f"  - 情感倾向: {emotional_tendency.get('overall_sentiment', 'N/A')}")
                    key_entities = analysis.get('key_entities', {})
                    persons = key_entities.get('persons', [])
                    print(f"  - 关键实体: {len(persons)} 个人物")

            # 辩论过程
            print(f"🔄 辩论过程:")
            print(f"  - 辩论轮数: {result['debate_rounds']}")
            print(f"  - 是否达成共识: {result['consensus_reached']}")

            # 候选标签
            candidate_labels = result.get('candidate_labels', [])
            print(f"📋 候选标签数量: {len(candidate_labels)}")

            # 高级评审
            senior_evaluation = result.get('senior_evaluation', {})
            if senior_evaluation:
                evaluation = senior_evaluation.get('evaluation', {})
                final_decision = evaluation.get('final_decision', {})
                print(f"⚖️ 高级评审:")
                print(f"  - 选中标签: {final_decision.get('selected_label_id', 'N/A')}")
                print(f"  - 评审置信度: {final_decision.get('confidence', 'N/A')}")

            # 最终结构化意图标签
            final_label = result.get('final_structured_intent_label', {})
            if final_label:
                print(f"🎯 最终结构化意图标签:")
                print(f"  - 事件背景: {final_label.get('event_background', 'N/A')}")
                print(f"  - 具体话题: {final_label.get('specific_topic', 'N/A')}")

                motivation = final_label.get('motivation', {})
                if isinstance(motivation, dict):
                    print(f"  - 用户动机: {motivation.get('description', 'N/A')}")
                    print(f"  - 深层目标: {motivation.get('deeper_goal', 'N/A')}")
                else:
                    print(f"  - 用户动机: {motivation}")

                print(f"  - 意图分类: {final_label.get('coarse_intent_category', 'N/A')}")

                behavior_set = final_label.get('behavior_set', [])
                if behavior_set:
                    print(f"  - 具体行为: {len(behavior_set)} 个行为")
                    for j, behavior in enumerate(behavior_set[:2]):  # 只显示前2个
                        if isinstance(behavior, dict):
                            print(f"    {j+1}. {behavior.get('behavior_type', 'N/A')}: {behavior.get('description', 'N/A')}")

                print(f"  - 目标群体: {final_label.get('target_group', 'N/A')}")
                print(f"  - 用户立场: {final_label.get('user_stance', 'N/A')}")
                print(f"  - 置信度得分: {final_label.get('confidence_score', 'N/A')}")

                key_evidence = final_label.get('key_evidence', [])
                if key_evidence:
                    print(f"  - 关键证据: {len(key_evidence)} 个证据片段")
                    for j, evidence in enumerate(key_evidence[:2]):  # 只显示前2个
                        print(f"    {j+1}. \"{evidence}\"")

            print(f"💾 结果已保存到: {output_path}")

            logger.info(f"用户 {user_id} 的细粒度意图分析完成")
            successful_analyses += 1

        except ValueError as e:
            # 内容安全检查失败，跳过该用户
            logger.warning(f"跳过用户 {user_id}: {e}")
            failed_analyses += 1

        except Exception as e:
            # 检查是否是DeepSeek内容风险错误
            error_msg = str(e)
            if "Content Exists Risk" in error_msg or "Content Risk" in error_msg:
                logger.warning(f"用户 {user_id} 触发内容风险检查，跳过")
                failed_analyses += 1
            else:
                logger.error(f"分析用户 {user_id} 时出错: {e}")
                import traceback
                traceback.print_exc()
                failed_analyses += 1

        # 添加延迟，避免过快请求
        time.sleep(2)

    # 输出最终统计
    logger.info("所有用户的细粒度意图分析已完成")
    logger.info(f"统计结果: 成功分析 {successful_analyses} 个用户，失败 {failed_analyses} 个用户")
    logger.info(f"成功率: {successful_analyses/(successful_analyses+failed_analyses)*100:.1f}%")


if __name__ == "__main__":
    main()