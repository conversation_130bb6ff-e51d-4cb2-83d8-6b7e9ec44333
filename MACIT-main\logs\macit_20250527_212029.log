2025-05-27 21:20:32,949 - test_10_users - INFO - 开始测试10个用户的意图分析...
2025-05-27 21:20:32,970 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:20:32,970 - data_utils - INFO - 成功加载 498 个用户画像
2025-05-27 21:20:32,970 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:20:32,970 - data_utils - INFO - 成功加载话题背景信息
2025-05-27 21:20:32,971 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:32,971 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:32,972 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:32,972 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:33,594 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:33,594 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,424 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:20:34,424 - model_interface - INFO - 成功创建智能体: context_analyst
2025-05-27 21:20:34,425 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,425 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,425 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:34,425 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:34,960 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,960 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,961 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:20:34,961 - model_interface - INFO - 成功创建智能体: intent_analyst_1
2025-05-27 21:20:34,962 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,962 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:34,962 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:34,962 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:35,636 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:35,636 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:35,637 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:20:35,637 - model_interface - INFO - 成功创建智能体: intent_analyst_2
2025-05-27 21:20:35,637 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:35,637 - model_interface - INFO - 使用通义千问平台创建模型，名称: qwen-max, URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:35,638 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:35,638 - model_interface - INFO - 模型配置: {'temperature': 0.5, 'max_tokens': 2048, 'top_p': 0.9}
2025-05-27 21:20:36,148 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:36,148 - model_interface - INFO - 成功创建自定义模型: qwen-max 使用通义千问平台，URL: https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation
2025-05-27 21:20:36,149 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:20:36,149 - model_interface - INFO - 成功创建智能体: senior_evaluator
2025-05-27 21:20:36,149 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:20:36,149 - macit_framework - INFO - MACIT框架初始化完成，包含4个智能体
2025-05-27 21:20:36,150 - test_10_users - INFO - MACIT框架初始化成功
2025-05-27 21:20:36,152 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:20:36,152 - data_utils - INFO - 找到 500 个用户ID
2025-05-27 21:20:36,152 - test_10_users - INFO - 将测试以下10个用户: ['1004599646080131072', '1021702922927669249', '1027405890314358784', '1034308064', '1037663718686031879', '1038128766575202306', '1058187164', '1064220744677232642', '1070439999517024257', '1070616509423280128']
2025-05-27 21:20:36,154 - test_10_users - INFO - 正在分析用户 1004599646080131072 (1/10)...
2025-05-27 21:20:36,155 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,155 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,156 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:20:36,156 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:20:36,156 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,156 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,157 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,157 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,158 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:20:36,158 - data_utils - INFO - 成功加载用户 1004599646080131072 的交互数据，共 21 条交互
2025-05-27 21:20:36,158 - data_utils - WARNING - 未找到用户 1004599646080131072 的画像
2025-05-27 21:20:36,158 - data_utils - WARNING - 未找到用户 1004599646080131072 的画像
2025-05-27 21:20:36,159 - macit_framework - WARNING - 未找到用户 1004599646080131072 的画像，使用空画像
2025-05-27 21:20:36,159 - macit_framework - WARNING - 未找到用户 1004599646080131072 的画像，使用空画像
2025-05-27 21:20:36,159 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,159 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,160 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,160 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,629 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:36,630 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:36,630 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:36,636 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,636 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,637 - test_10_users - ERROR - 分析用户 1004599646080131072 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,638 - test_10_users - INFO - 正在分析用户 1021702922927669249 (2/10)...
2025-05-27 21:20:36,639 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,639 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,640 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:20:36,640 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:20:36,640 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,640 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,640 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,640 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,641 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:20:36,641 - data_utils - INFO - 成功加载用户 1021702922927669249 的交互数据，共 18 条交互
2025-05-27 21:20:36,641 - data_utils - WARNING - 未找到用户 1021702922927669249 的画像
2025-05-27 21:20:36,641 - data_utils - WARNING - 未找到用户 1021702922927669249 的画像
2025-05-27 21:20:36,642 - macit_framework - WARNING - 未找到用户 1021702922927669249 的画像，使用空画像
2025-05-27 21:20:36,642 - macit_framework - WARNING - 未找到用户 1021702922927669249 的画像，使用空画像
2025-05-27 21:20:36,642 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,642 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,642 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,642 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,730 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:36,730 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:36,731 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:36,734 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,734 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,734 - test_10_users - ERROR - 分析用户 1021702922927669249 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,735 - test_10_users - INFO - 正在分析用户 1027405890314358784 (3/10)...
2025-05-27 21:20:36,735 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,735 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,736 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:20:36,736 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:20:36,737 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,737 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,737 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,737 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,738 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:20:36,738 - data_utils - INFO - 成功加载用户 1027405890314358784 的交互数据，共 15 条交互
2025-05-27 21:20:36,738 - data_utils - WARNING - 未找到用户 1027405890314358784 的画像
2025-05-27 21:20:36,738 - data_utils - WARNING - 未找到用户 1027405890314358784 的画像
2025-05-27 21:20:36,738 - macit_framework - WARNING - 未找到用户 1027405890314358784 的画像，使用空画像
2025-05-27 21:20:36,738 - macit_framework - WARNING - 未找到用户 1027405890314358784 的画像，使用空画像
2025-05-27 21:20:36,738 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,738 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,739 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,739 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,823 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:36,824 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:36,824 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:36,827 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,827 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,828 - test_10_users - ERROR - 分析用户 1027405890314358784 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,828 - test_10_users - INFO - 正在分析用户 1034308064 (4/10)...
2025-05-27 21:20:36,829 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,829 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,830 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:20:36,830 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:20:36,830 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,830 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,830 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,830 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,831 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:20:36,831 - data_utils - INFO - 成功加载用户 1034308064 的交互数据，共 25 条交互
2025-05-27 21:20:36,831 - data_utils - WARNING - 未找到用户 1034308064 的画像
2025-05-27 21:20:36,831 - data_utils - WARNING - 未找到用户 1034308064 的画像
2025-05-27 21:20:36,832 - macit_framework - WARNING - 未找到用户 1034308064 的画像，使用空画像
2025-05-27 21:20:36,832 - macit_framework - WARNING - 未找到用户 1034308064 的画像，使用空画像
2025-05-27 21:20:36,832 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,832 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,832 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,832 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,915 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:36,915 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:36,916 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:36,918 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,918 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,919 - test_10_users - ERROR - 分析用户 1034308064 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:36,919 - test_10_users - INFO - 正在分析用户 1037663718686031879 (5/10)...
2025-05-27 21:20:36,920 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,920 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:36,921 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:20:36,921 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:20:36,921 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,921 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,922 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,922 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:36,922 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:20:36,922 - data_utils - INFO - 成功加载用户 1037663718686031879 的交互数据，共 16 条交互
2025-05-27 21:20:36,923 - data_utils - WARNING - 未找到用户 1037663718686031879 的画像
2025-05-27 21:20:36,923 - data_utils - WARNING - 未找到用户 1037663718686031879 的画像
2025-05-27 21:20:36,923 - macit_framework - WARNING - 未找到用户 1037663718686031879 的画像，使用空画像
2025-05-27 21:20:36,923 - macit_framework - WARNING - 未找到用户 1037663718686031879 的画像，使用空画像
2025-05-27 21:20:36,923 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,923 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:36,924 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:36,924 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,014 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,015 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,015 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,019 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,019 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,019 - test_10_users - ERROR - 分析用户 1037663718686031879 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,020 - test_10_users - INFO - 正在分析用户 1038128766575202306 (6/10)...
2025-05-27 21:20:37,020 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,020 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,021 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:20:37,021 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:20:37,022 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,022 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,022 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,022 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,023 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:20:37,023 - data_utils - INFO - 成功加载用户 1038128766575202306 的交互数据，共 18 条交互
2025-05-27 21:20:37,024 - data_utils - WARNING - 未找到用户 1038128766575202306 的画像
2025-05-27 21:20:37,024 - data_utils - WARNING - 未找到用户 1038128766575202306 的画像
2025-05-27 21:20:37,024 - macit_framework - WARNING - 未找到用户 1038128766575202306 的画像，使用空画像
2025-05-27 21:20:37,024 - macit_framework - WARNING - 未找到用户 1038128766575202306 的画像，使用空画像
2025-05-27 21:20:37,025 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,025 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,025 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,025 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,115 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,116 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,116 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,119 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,119 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,119 - test_10_users - ERROR - 分析用户 1038128766575202306 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,119 - test_10_users - INFO - 正在分析用户 1058187164 (7/10)...
2025-05-27 21:20:37,120 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,120 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,122 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:20:37,122 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:20:37,122 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,122 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,122 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,122 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,123 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:20:37,123 - data_utils - INFO - 成功加载用户 1058187164 的交互数据，共 23 条交互
2025-05-27 21:20:37,124 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-27 21:20:37,124 - data_utils - WARNING - 未找到用户 1058187164 的画像
2025-05-27 21:20:37,124 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-27 21:20:37,124 - macit_framework - WARNING - 未找到用户 1058187164 的画像，使用空画像
2025-05-27 21:20:37,124 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,124 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,124 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,124 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,210 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,212 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,212 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,214 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,214 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,215 - test_10_users - ERROR - 分析用户 1058187164 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,215 - test_10_users - INFO - 正在分析用户 1064220744677232642 (8/10)...
2025-05-27 21:20:37,216 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,216 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,217 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:20:37,217 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:20:37,217 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,217 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,218 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,218 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,219 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:20:37,219 - data_utils - INFO - 成功加载用户 1064220744677232642 的交互数据，共 23 条交互
2025-05-27 21:20:37,219 - data_utils - WARNING - 未找到用户 1064220744677232642 的画像
2025-05-27 21:20:37,219 - data_utils - WARNING - 未找到用户 1064220744677232642 的画像
2025-05-27 21:20:37,219 - macit_framework - WARNING - 未找到用户 1064220744677232642 的画像，使用空画像
2025-05-27 21:20:37,219 - macit_framework - WARNING - 未找到用户 1064220744677232642 的画像，使用空画像
2025-05-27 21:20:37,219 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,219 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,221 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,221 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,305 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,306 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,306 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,309 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,309 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,310 - test_10_users - ERROR - 分析用户 1064220744677232642 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,310 - test_10_users - INFO - 正在分析用户 1070439999517024257 (9/10)...
2025-05-27 21:20:37,311 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,311 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,312 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:20:37,312 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:20:37,312 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,312 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,312 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,312 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,313 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:20:37,313 - data_utils - INFO - 成功加载用户 1070439999517024257 的交互数据，共 21 条交互
2025-05-27 21:20:37,313 - data_utils - WARNING - 未找到用户 1070439999517024257 的画像
2025-05-27 21:20:37,313 - data_utils - WARNING - 未找到用户 1070439999517024257 的画像
2025-05-27 21:20:37,314 - macit_framework - WARNING - 未找到用户 1070439999517024257 的画像，使用空画像
2025-05-27 21:20:37,314 - macit_framework - WARNING - 未找到用户 1070439999517024257 的画像，使用空画像
2025-05-27 21:20:37,314 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,314 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,315 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,315 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,399 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,399 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,400 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,402 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,402 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,403 - test_10_users - ERROR - 分析用户 1070439999517024257 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,404 - test_10_users - INFO - 正在分析用户 1070616509423280128 (10/10)...
2025-05-27 21:20:37,404 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,404 - macit_framework - WARNING - 重置智能体上下文时出现警告: 'MACITFramework' object has no attribute 'intent_agent_1'
2025-05-27 21:20:37,406 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:20:37,406 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:20:37,406 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,406 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,407 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,407 - macit_framework - INFO - 内容安全检查已禁用，忽略安全检查
2025-05-27 21:20:37,408 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:20:37,408 - data_utils - INFO - 成功加载用户 1070616509423280128 的交互数据，共 23 条交互
2025-05-27 21:20:37,408 - data_utils - WARNING - 未找到用户 1070616509423280128 的画像
2025-05-27 21:20:37,408 - data_utils - WARNING - 未找到用户 1070616509423280128 的画像
2025-05-27 21:20:37,409 - macit_framework - WARNING - 未找到用户 1070616509423280128 的画像，使用空画像
2025-05-27 21:20:37,409 - macit_framework - WARNING - 未找到用户 1070616509423280128 的画像，使用空画像
2025-05-27 21:20:37,409 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,409 - macit_framework - INFO - 第一步：生成上下文分析报告
2025-05-27 21:20:37,409 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,409 - macit_framework - INFO - 开始生成上下文分析报告
2025-05-27 21:20:37,496 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/v1/services/aigc/text-generation/generation/chat/completions "HTTP/1.1 404 Not Found"
2025-05-27 21:20:37,497 - camel.models.model_manager - ERROR - Error processing with model: <camel.models.openai_compatible_model.OpenAICompatibleModel object at 0x0000021CF37C21D0>
2025-05-27 21:20:37,497 - camel.agents.chat_agent - ERROR - An error occurred while running model qwen-max, index: 0
Traceback (most recent call last):
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\agents\chat_agent.py", line 1032, in _get_model_response
    response = self.model_backend.run(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 226, in run
    raise exc
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\model_manager.py", line 216, in run
    response = self.current_model.run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 50, in wrapped_run
    return original_run(self, messages, *args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\base_model.py", line 278, in run
    return self._run(messages, response_format, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 116, in _run
    return self._request_chat_completion(messages, tools)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\camel\models\openai_compatible_model.py", line 157, in _request_chat_completion
    return self._client.chat.completions.create(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\resources\chat\completions\completions.py", line 925, in create
    return self._post(
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1239, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "D:\cskaoyan\anaconda\envs\MACIT\lib\site-packages\openai\_base_client.py", line 1034, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404
2025-05-27 21:20:37,500 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,500 - macit_framework - ERROR - 上下文分析失败: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,500 - test_10_users - ERROR - 分析用户 1070616509423280128 时出错: Unable to process messages: the only provided model did not run successfully. Error: Error code: 404
2025-05-27 21:20:37,501 - test_10_users - INFO - 所有用户分析完成，正在整理结果...
2025-05-27 21:20:37,502 - test_10_users - INFO - 格式化结果已保存至: output\test_10_users_results.txt
